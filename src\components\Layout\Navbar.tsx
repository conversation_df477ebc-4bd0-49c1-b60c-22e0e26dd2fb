import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Menu, X, User, Settings, LogOut } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useStore } from '../../store/useStore';

const Navbar: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const { currentUser, logout } = useAuth();
  const { user, currentLanguage, setLanguage } = useStore();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const toggleLanguage = () => {
    const newLanguage = currentLanguage === 'en' ? 'si' : 'en';
    setLanguage(newLanguage);
  };

  return (
    <nav className="bg-dark-800/90 backdrop-blur-sm border-b border-dark-700 sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-mythology-gold rounded-full flex items-center justify-center">
              <span className="text-dark-900 font-bold text-lg">K</span>
            </div>
            <span className="mythology-title text-xl font-bold">Kubera</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link to="/horoscope" className="text-gray-300 hover:text-mythology-gold transition-colors">
              Horoscope
            </Link>
            <Link to="/future-reading" className="text-gray-300 hover:text-mythology-gold transition-colors">
              Future Reading
            </Link>
            <Link to="/daily-guidance" className="text-gray-300 hover:text-mythology-gold transition-colors">
              Daily Guidance
            </Link>
            
            {/* Language Toggle */}
            <button
              onClick={toggleLanguage}
              className="px-3 py-1 text-sm bg-dark-700 text-gray-300 rounded-md hover:bg-dark-600 transition-colors"
            >
              {currentLanguage === 'en' ? 'සිං' : 'EN'}
            </button>

            {/* User Menu */}
            {currentUser ? (
              <div className="relative">
                <button
                  onClick={() => setIsProfileOpen(!isProfileOpen)}
                  className="flex items-center space-x-2 text-gray-300 hover:text-mythology-gold transition-colors"
                >
                  <User size={20} />
                  <span className="hidden lg:block">{user?.displayName || 'User'}</span>
                </button>

                {isProfileOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-dark-800 rounded-md shadow-lg border border-dark-700 py-1">
                    <Link
                      to="/profile"
                      className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-mythology-gold"
                      onClick={() => setIsProfileOpen(false)}
                    >
                      <User size={16} className="mr-2" />
                      Profile
                    </Link>
                    <Link
                      to="/settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-mythology-gold"
                      onClick={() => setIsProfileOpen(false)}
                    >
                      <Settings size={16} className="mr-2" />
                      Settings
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-gray-300 hover:bg-dark-700 hover:text-red-400"
                    >
                      <LogOut size={16} className="mr-2" />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  to="/login"
                  className="text-gray-300 hover:text-mythology-gold transition-colors"
                >
                  Login
                </Link>
                <Link
                  to="/register"
                  className="mythology-button px-4 py-2 text-sm"
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden text-gray-300 hover:text-mythology-gold"
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-dark-700">
            <div className="flex flex-col space-y-4">
              <Link
                to="/horoscope"
                className="text-gray-300 hover:text-mythology-gold transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Horoscope
              </Link>
              <Link
                to="/future-reading"
                className="text-gray-300 hover:text-mythology-gold transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Future Reading
              </Link>
              <Link
                to="/daily-guidance"
                className="text-gray-300 hover:text-mythology-gold transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Daily Guidance
              </Link>
              
              <button
                onClick={toggleLanguage}
                className="text-left px-3 py-1 text-sm bg-dark-700 text-gray-300 rounded-md hover:bg-dark-600 transition-colors w-fit"
              >
                {currentLanguage === 'en' ? 'සිංහල' : 'English'}
              </button>

              {currentUser ? (
                <>
                  <Link
                    to="/profile"
                    className="text-gray-300 hover:text-mythology-gold transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Profile
                  </Link>
                  <Link
                    to="/settings"
                    className="text-gray-300 hover:text-mythology-gold transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Settings
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="text-left text-gray-300 hover:text-red-400 transition-colors"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link
                    to="/login"
                    className="text-gray-300 hover:text-mythology-gold transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Login
                  </Link>
                  <Link
                    to="/register"
                    className="mythology-button px-4 py-2 text-sm w-fit"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign Up
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
