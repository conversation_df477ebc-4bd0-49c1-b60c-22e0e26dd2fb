import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Star, Sparkles, RefreshCw } from 'lucide-react';
import { useStore } from '../store/useStore';
import { generateHoroscope } from '../config/apis';
import { zodiacSigns } from '../data/zodiacSigns';
import { format } from 'date-fns';

const Horoscope: React.FC = () => {
  const { user, horoscopes, addHoroscope, setLoading, isLoading, setError } = useStore();
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [selectedZodiac, setSelectedZodiac] = useState(user?.zodiacSign || 'aries');

  const periods = [
    { key: 'daily', label: 'Daily', icon: <Calendar size={20} /> },
    { key: 'weekly', label: 'Weekly', icon: <Star size={20} /> },
    { key: 'monthly', label: 'Monthly', icon: <Sparkles size={20} /> }
  ];

  const handleGenerateHoroscope = async () => {
    if (!selectedZodiac) return;

    setLoading(true);
    setError(null);

    try {
      const content = await generateHoroscope(selectedZodiac, selectedPeriod, user);
      
      const newHoroscope = {
        id: Date.now().toString(),
        zodiacSign: selectedZodiac,
        period: selectedPeriod,
        content,
        date: format(new Date(), 'yyyy-MM-dd'),
      };

      addHoroscope(newHoroscope);
    } catch (error: any) {
      setError(error.message || 'Failed to generate horoscope');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentHoroscope = () => {
    const today = format(new Date(), 'yyyy-MM-dd');
    return horoscopes.find(h => 
      h.zodiacSign === selectedZodiac && 
      h.period === selectedPeriod && 
      h.date === today
    );
  };

  const currentHoroscope = getCurrentHoroscope();
  const zodiacInfo = zodiacSigns[selectedZodiac];

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center mb-8">
          <h1 className="mythology-title text-4xl font-bold mb-4">Your Horoscope</h1>
          <p className="text-xl text-gray-400">
            Discover what the stars have aligned for you
          </p>
        </div>

        {/* Zodiac Selection */}
        <div className="mythology-card p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-mythology-gold">Select Your Sign</h2>
          <div className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-12 gap-3">
            {Object.entries(zodiacSigns).map(([key, sign]) => (
              <button
                key={key}
                onClick={() => setSelectedZodiac(key)}
                className={`p-3 rounded-lg border-2 transition-all duration-300 ${
                  selectedZodiac === key
                    ? 'border-mythology-gold bg-mythology-gold/20 text-mythology-gold'
                    : 'border-dark-600 hover:border-mythology-gold/50 text-gray-400 hover:text-gray-300'
                }`}
              >
                <div className="text-2xl mb-1">{sign.symbol}</div>
                <div className="text-xs font-medium">{sign.name}</div>
              </button>
            ))}
          </div>
        </div>

        {/* Period Selection */}
        <div className="mythology-card p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-mythology-gold">Reading Period</h2>
          <div className="flex flex-wrap gap-4">
            {periods.map((period) => (
              <button
                key={period.key}
                onClick={() => setSelectedPeriod(period.key as any)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg border-2 transition-all duration-300 ${
                  selectedPeriod === period.key
                    ? 'border-mythology-gold bg-mythology-gold/20 text-mythology-gold'
                    : 'border-dark-600 hover:border-mythology-gold/50 text-gray-400 hover:text-gray-300'
                }`}
              >
                {period.icon}
                <span className="font-medium">{period.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Zodiac Info */}
        {zodiacInfo && (
          <div className="mythology-card p-6 mb-8">
            <div className="flex items-center mb-4">
              <div className="text-4xl mr-4">{zodiacInfo.symbol}</div>
              <div>
                <h3 className="text-2xl font-semibold text-mythology-gold">{zodiacInfo.name}</h3>
                <p className="text-gray-400">{zodiacInfo.dates}</p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-mythology-gold font-medium">Element:</span> {zodiacInfo.element}
              </div>
              <div>
                <span className="text-mythology-gold font-medium">Planet:</span> {zodiacInfo.planet}
              </div>
              <div>
                <span className="text-mythology-gold font-medium">Gemstone:</span> {zodiacInfo.gemstone}
              </div>
              <div>
                <span className="text-mythology-gold font-medium">Color:</span> 
                <span 
                  className="inline-block w-4 h-4 rounded-full ml-2 align-middle"
                  style={{ backgroundColor: zodiacInfo.color }}
                ></span>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-mythology-gold font-medium mb-2">Mythology:</p>
              <p className="text-gray-300 text-sm">{zodiacInfo.mythology}</p>
            </div>
          </div>
        )}

        {/* Generate Button */}
        <div className="text-center mb-8">
          <button
            onClick={handleGenerateHoroscope}
            disabled={isLoading}
            className="mythology-button px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto"
          >
            {isLoading ? (
              <>
                <RefreshCw className="animate-spin" size={20} />
                <span>Consulting the Stars...</span>
              </>
            ) : (
              <>
                <Sparkles size={20} />
                <span>Get My {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)} Reading</span>
              </>
            )}
          </button>
        </div>

        {/* Current Horoscope */}
        {currentHoroscope && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="mythology-card p-8"
          >
            <div className="text-center mb-6">
              <h3 className="text-2xl font-semibold text-mythology-gold mb-2">
                {zodiacInfo.name} {selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)} Reading
              </h3>
              <p className="text-gray-400">
                {format(new Date(currentHoroscope.date), 'MMMM d, yyyy')}
              </p>
            </div>
            
            <div className="prose prose-invert max-w-none">
              <p className="text-gray-300 leading-relaxed text-lg whitespace-pre-line">
                {currentHoroscope.content}
              </p>
            </div>
          </motion.div>
        )}

        {/* Recent Horoscopes */}
        {horoscopes.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-semibold mb-6 text-mythology-gold">Recent Readings</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {horoscopes.slice(0, 4).map((horoscope) => (
                <motion.div
                  key={horoscope.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mythology-card p-6"
                >
                  <div className="flex items-center mb-3">
                    <span className="text-2xl mr-3">
                      {zodiacSigns[horoscope.zodiacSign].symbol}
                    </span>
                    <div>
                      <h4 className="font-semibold text-white">
                        {zodiacSigns[horoscope.zodiacSign].name}
                      </h4>
                      <p className="text-sm text-gray-400">
                        {horoscope.period} • {format(new Date(horoscope.date), 'MMM d')}
                      </p>
                    </div>
                  </div>
                  <p className="text-gray-300 text-sm line-clamp-3">
                    {horoscope.content}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default Horoscope;
