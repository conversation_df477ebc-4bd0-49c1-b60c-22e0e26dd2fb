/* Import Google Fonts for mythology theme */
@import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Mythology Theme */
:root {
  --mythology-gold: #d4af37;
  --mythology-bronze: #cd7f32;
  --mythology-silver: #c0c0c0;
  --mythology-purple: #6a0dad;
  --mythology-crimson: #dc143c;
  --dark-bg: #0f172a;
  --dark-surface: #1e293b;
  --dark-border: #334155;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: #f8fafc;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Typography */
.mythology-title {
  font-family: 'Cinzel', serif;
  font-weight: 600;
  color: var(--mythology-gold);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.mythology-text {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
}

/* Custom Components */
.mythology-card {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border: 1px solid #475569;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.mythology-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: var(--mythology-gold);
}

.mythology-button {
  background: linear-gradient(135deg, var(--mythology-gold) 0%, #b8941f 100%);
  color: #0f172a;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mythology-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
}

.mythology-button:active {
  transform: translateY(0);
}

/* Loading Animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Glow Effect */
.glow-effect {
  box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(212, 175, 55, 0.5); }
  50% { box-shadow: 0 0 20px rgba(212, 175, 55, 0.8); }
}
