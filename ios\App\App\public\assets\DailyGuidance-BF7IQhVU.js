import{c as w,u as I,r as m,b as S,j as e,m as x,z as M,d as z,a as G,M as D}from"./index-QubfbT_B.js";import{R as $}from"./refresh-cw-BMeRD5eS.js";import{f as h}from"./format-CBpsKyOP.js";/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],g=w("circle-check-big",R);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E=[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],v=w("compass",E),L=()=>{const{user:r,dailyInstructions:a,setDailyInstructions:k}=I(),[o,y]=m.useState(null),[n,d]=m.useState([]),p=(r==null?void 0:r.zodiacSign)||"aries",l=M[p],i=S(p);m.useEffect(()=>{const t=localStorage.getItem(`completed-instructions-${h(new Date,"yyyy-MM-dd")}`);t&&d(JSON.parse(t))},[]);const u=t=>{const s=[...n,t];d(s),localStorage.setItem(`completed-instructions-${h(new Date,"yyyy-MM-dd")}`,JSON.stringify(s))},j=t=>{const s=n.filter(c=>c!==t);d(s),localStorage.setItem(`completed-instructions-${h(new Date,"yyyy-MM-dd")}`,JSON.stringify(s))},C=()=>{const t=i.filter(s=>!n.includes(s));return t.length===0?i[Math.floor(Math.random()*i.length)]:t[Math.floor(Math.random()*t.length)]},N=()=>{const t=C();y(t),k(t)},f=n.length/i.length*100;return e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs(x.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6},children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"mythology-title text-4xl font-bold mb-4",children:"Daily Guidance"}),e.jsx("p",{className:"text-xl text-gray-400",children:"Wisdom tailored to your cosmic nature"})]}),e.jsxs("div",{className:"mythology-card p-6 mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-4xl",children:l.symbol}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-semibold text-mythology-gold",children:l.name}),e.jsx("p",{className:"text-gray-400",children:l.dates})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm text-gray-400",children:"Today's Progress"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-20 h-2 bg-dark-600 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-mythology-gold transition-all duration-500",style:{width:`${f}%`}})}),e.jsxs("span",{className:"text-sm text-mythology-gold font-medium",children:[Math.round(f),"%"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[e.jsxs("div",{className:"text-center p-3 bg-dark-700/50 rounded-lg",children:[e.jsx(z,{className:"w-6 h-6 text-mythology-gold mx-auto mb-2"}),e.jsx("p",{className:"text-gray-400",children:"Element"}),e.jsx("p",{className:"text-white font-medium",children:l.element})]}),e.jsxs("div",{className:"text-center p-3 bg-dark-700/50 rounded-lg",children:[e.jsx(G,{className:"w-6 h-6 text-mythology-gold mx-auto mb-2"}),e.jsx("p",{className:"text-gray-400",children:"Ruling Planet"}),e.jsx("p",{className:"text-white font-medium",children:l.planet})]}),e.jsxs("div",{className:"text-center p-3 bg-dark-700/50 rounded-lg",children:[e.jsx(D,{className:"w-6 h-6 text-mythology-gold mx-auto mb-2"}),e.jsx("p",{className:"text-gray-400",children:"Gemstone"}),e.jsx("p",{className:"text-white font-medium",children:l.gemstone})]})]})]}),e.jsxs("div",{className:"mythology-card p-8 mb-8 text-center",children:[e.jsx(v,{className:"w-12 h-12 text-mythology-gold mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-semibold mb-6 text-mythology-gold",children:"Today's Guidance"}),o||a?e.jsxs(x.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5},className:"space-y-6",children:[e.jsx("p",{className:"text-xl text-gray-300 leading-relaxed",children:o||a}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[n.includes(o||a||"")?e.jsxs("button",{onClick:()=>j(o||a||""),className:"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2",children:[e.jsx(g,{size:20}),e.jsx("span",{children:"Completed ✓"})]}):e.jsxs("button",{onClick:()=>u(o||a||""),className:"mythology-button px-6 py-3 flex items-center space-x-2",children:[e.jsx(g,{size:20}),e.jsx("span",{children:"Mark as Completed"})]}),e.jsxs("button",{onClick:N,className:"px-6 py-3 border border-mythology-gold text-mythology-gold rounded-lg hover:bg-mythology-gold hover:text-dark-900 transition-all duration-300 flex items-center space-x-2",children:[e.jsx($,{size:20}),e.jsx("span",{children:"New Guidance"})]})]})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsx("p",{className:"text-gray-400",children:"Ready to receive your personalized guidance for today?"}),e.jsxs("button",{onClick:N,className:"mythology-button px-8 py-4 text-lg flex items-center space-x-2 mx-auto",children:[e.jsx(v,{size:20}),e.jsx("span",{children:"Get Today's Guidance"})]})]})]}),e.jsxs("div",{className:"mythology-card p-6",children:[e.jsxs("h2",{className:"text-2xl font-semibold mb-6 text-mythology-gold",children:[l.name," Guidance Collection"]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:i.map((t,s)=>{const c=n.includes(t),b=t===(o||a);return e.jsxs(x.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:s*.1},className:`p-4 rounded-lg border-2 transition-all duration-300 ${b?"border-mythology-gold bg-mythology-gold/10":c?"border-green-500/50 bg-green-500/10":"border-dark-600 hover:border-dark-500"}`,children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsx("p",{className:`text-sm leading-relaxed flex-1 ${c?"text-gray-400 line-through":"text-gray-300"}`,children:t}),e.jsxs("div",{className:"ml-3 flex flex-col space-y-2",children:[b&&e.jsx("div",{className:"w-3 h-3 bg-mythology-gold rounded-full animate-pulse"}),c&&e.jsx(g,{className:"w-5 h-5 text-green-500"})]})]}),e.jsxs("div",{className:"mt-3 flex space-x-2",children:[e.jsx("button",{onClick:()=>y(t),className:"text-xs text-mythology-gold hover:text-mythology-bronze transition-colors",children:"Set as Current"}),c?e.jsx("button",{onClick:()=>j(t),className:"text-xs text-gray-400 hover:text-gray-300 transition-colors",children:"Undo"}):e.jsx("button",{onClick:()=>u(t),className:"text-xs text-green-400 hover:text-green-300 transition-colors",children:"Complete"})]})]},s)})})]}),e.jsxs("div",{className:"mythology-card p-6 mt-8",children:[e.jsxs("h2",{className:"text-2xl font-semibold mb-4 text-mythology-gold",children:["Your ",l.name," Traits"]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:l.traits.map((t,s)=>e.jsx("span",{className:"px-3 py-1 bg-mythology-gold/20 text-mythology-gold rounded-full text-sm font-medium",children:t},s))}),e.jsx("div",{className:"mt-4",children:e.jsx("p",{className:"text-gray-300 text-sm leading-relaxed",children:l.mythology})})]})]})})};export{L as default};
