import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Star, Moon, Sun, Sparkles, ArrowRight } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useStore } from '../store/useStore';

const Home: React.FC = () => {
  const { currentUser } = useAuth();
  const { user } = useStore();

  const features = [
    {
      icon: <Sun className="w-8 h-8" />,
      title: 'Daily Horoscope',
      description: 'Get personalized daily insights based on your zodiac sign and cosmic alignments.',
      link: '/horoscope'
    },
    {
      icon: <Moon className="w-8 h-8" />,
      title: 'Future Readings',
      description: 'Discover what the stars have in store for your future with AI-powered predictions.',
      link: '/future-reading'
    },
    {
      icon: <Star className="w-8 h-8" />,
      title: 'Daily Guidance',
      description: 'Receive wisdom-based instructions tailored to your zodiac characteristics.',
      link: '/daily-guidance'
    },
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: 'Mythology Insights',
      description: 'Explore the rich mythology behind your zodiac sign and cosmic connections.',
      link: '/mythology'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-mythology-purple/20 to-mythology-gold/20 rounded-3xl"></div>
        <div className="relative z-10 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="mythology-title text-5xl md:text-7xl font-bold mb-6">
              Welcome to Kubera
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Unlock the mysteries of your destiny through ancient wisdom and modern AI. 
              Discover personalized horoscopes, future insights, and daily guidance.
            </p>
            
            {currentUser ? (
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link
                  to="/horoscope"
                  className="mythology-button px-8 py-4 text-lg flex items-center space-x-2"
                >
                  <span>Get My Reading</span>
                  <ArrowRight size={20} />
                </Link>
                <p className="text-gray-400">
                  Welcome back, {user?.displayName || 'Seeker'}!
                </p>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/register"
                  className="mythology-button px-8 py-4 text-lg flex items-center space-x-2"
                >
                  <span>Begin Your Journey</span>
                  <ArrowRight size={20} />
                </Link>
                <Link
                  to="/login"
                  className="px-8 py-4 text-lg border border-mythology-gold text-mythology-gold rounded-lg hover:bg-mythology-gold hover:text-dark-900 transition-all duration-300"
                >
                  Sign In
                </Link>
              </div>
            )}
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="text-center mb-16">
          <h2 className="mythology-title text-4xl font-bold mb-4">
            Discover Your Cosmic Path
          </h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Explore our mystical services designed to guide you through life's journey
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Link to={feature.link} className="block">
                <div className="mythology-card p-6 h-full hover:scale-105 transition-transform duration-300">
                  <div className="text-mythology-gold mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-white">
                    {feature.title}
                  </h3>
                  <p className="text-gray-400 mb-4">
                    {feature.description}
                  </p>
                  <div className="flex items-center text-mythology-gold hover:text-mythology-bronze transition-colors">
                    <span className="text-sm font-medium">Explore</span>
                    <ArrowRight size={16} className="ml-2" />
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-dark-800/50 rounded-3xl">
        <div className="text-center mb-16">
          <h2 className="mythology-title text-4xl font-bold mb-4">
            What Seekers Say
          </h2>
          <p className="text-xl text-gray-400">
            Join thousands who have found guidance through Kubera
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[
            {
              name: "Sarah M.",
              text: "Kubera's daily guidance has transformed how I approach each day. The insights are incredibly accurate!",
              zodiac: "Leo"
            },
            {
              name: "Michael R.",
              text: "The future readings helped me make important life decisions with confidence. Truly remarkable!",
              zodiac: "Scorpio"
            },
            {
              name: "Priya K.",
              text: "I love how the app combines ancient wisdom with modern technology. The Sinhala translation is perfect!",
              zodiac: "Pisces"
            }
          ].map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              className="mythology-card p-6"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-mythology-gold rounded-full flex items-center justify-center mr-4">
                  <Star className="text-dark-900" size={20} />
                </div>
                <div>
                  <h4 className="font-semibold text-white">{testimonial.name}</h4>
                  <p className="text-sm text-mythology-gold">{testimonial.zodiac}</p>
                </div>
              </div>
              <p className="text-gray-300 italic">"{testimonial.text}"</p>
            </motion.div>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="mythology-title text-4xl font-bold mb-6">
            Ready to Discover Your Destiny?
          </h2>
          <p className="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
            Join the cosmic journey and unlock the secrets that the universe has in store for you.
          </p>
          {!currentUser && (
            <Link
              to="/register"
              className="mythology-button px-8 py-4 text-lg inline-flex items-center space-x-2"
            >
              <span>Start Your Journey</span>
              <Sparkles size={20} />
            </Link>
          )}
        </motion.div>
      </section>
    </div>
  );
};

export default Home;
