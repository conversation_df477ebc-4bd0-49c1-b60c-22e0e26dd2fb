import{c as o,e as S,u as A,r as g,j as e,m as u,M as y,d as D,h as M}from"./index-QubfbT_B.js";import{S as L}from"./save-CsSdIJd3.js";/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],$=o("bell",_);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],E=o("globe",z);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],I=o("trash-2",R),F=()=>{var d,m,h;const{updateUserProfile:f,logout:p}=S(),{user:t,setLanguage:b,setLoading:n,isLoading:i,setError:r,clearUserData:j}=A(),[a,N]=g.useState({language:((d=t==null?void 0:t.preferences)==null?void 0:d.language)||"en",notifications:((m=t==null?void 0:t.preferences)==null?void 0:m.notifications)??!0,theme:((h=t==null?void 0:t.preferences)==null?void 0:h.theme)||"dark"}),[v,c]=g.useState(!1),l=(s,x)=>{N(C=>({...C,[s]:x})),s==="language"&&b(x)},w=async()=>{if(t){n(!0),r(null);try{await f({...t,preferences:{...t.preferences,...a}})}catch(s){r(s.message||"Failed to update settings")}finally{n(!1)}}},k=async()=>{try{await p(),j()}catch(s){r(s.message||"Failed to delete account")}};return e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs(u.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6},children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"mythology-title text-4xl font-bold mb-4",children:"Settings"}),e.jsx("p",{className:"text-xl text-gray-400",children:"Customize your cosmic experience"})]}),e.jsxs("div",{className:"mythology-card p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(E,{className:"w-6 h-6 text-mythology-gold mr-3"}),e.jsx("h2",{className:"text-xl font-semibold text-white",children:"Language & Region"})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Preferred Language"}),e.jsxs("select",{value:a.language,onChange:s=>l("language",s.target.value),className:"w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"si",children:"සිංහල (Sinhala)"})]}),e.jsx("p",{className:"text-sm text-gray-400 mt-2",children:"All content will be translated to your preferred language using AI"})]})})]}),e.jsxs("div",{className:"mythology-card p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx($,{className:"w-6 h-6 text-mythology-gold mr-3"}),e.jsx("h2",{className:"text-xl font-semibold text-white",children:"Notifications"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-medium",children:"Daily Horoscope Reminders"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Get notified when your daily reading is ready"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.notifications,onChange:s=>l("notifications",s.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-dark-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-mythology-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-mythology-gold"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-medium",children:"Weekly Insights"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Receive weekly cosmic insights and guidance"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.notifications,onChange:s=>l("notifications",s.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-dark-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-mythology-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-mythology-gold"})]})]})]})]}),e.jsxs("div",{className:"mythology-card p-6 mb-6",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(y,{className:"w-6 h-6 text-mythology-gold mr-3"}),e.jsx("h2",{className:"text-xl font-semibold text-white",children:"Appearance"})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Theme"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("button",{onClick:()=>l("theme","dark"),className:`p-4 rounded-lg border-2 transition-all duration-300 ${a.theme==="dark"?"border-mythology-gold bg-mythology-gold/20":"border-dark-600 hover:border-mythology-gold/50"}`,children:[e.jsx(y,{className:"w-6 h-6 text-mythology-gold mx-auto mb-2"}),e.jsx("p",{className:"text-white font-medium",children:"Dark"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Current theme"})]}),e.jsxs("button",{onClick:()=>l("theme","light"),className:`p-4 rounded-lg border-2 transition-all duration-300 opacity-50 cursor-not-allowed ${a.theme==="light"?"border-mythology-gold bg-mythology-gold/20":"border-dark-600"}`,disabled:!0,children:[e.jsx(D,{className:"w-6 h-6 text-mythology-gold mx-auto mb-2"}),e.jsx("p",{className:"text-white font-medium",children:"Light"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Coming soon"})]}),e.jsxs("button",{onClick:()=>l("theme","auto"),className:`p-4 rounded-lg border-2 transition-all duration-300 opacity-50 cursor-not-allowed ${a.theme==="auto"?"border-mythology-gold bg-mythology-gold/20":"border-dark-600"}`,disabled:!0,children:[e.jsx(M,{className:"w-6 h-6 text-mythology-gold mx-auto mb-2"}),e.jsx("p",{className:"text-white font-medium",children:"Auto"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Coming soon"})]})]})]})})]}),e.jsx("div",{className:"text-center mb-8",children:e.jsxs("button",{onClick:w,disabled:i,className:"mythology-button px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto",children:[e.jsx(L,{size:20}),e.jsx("span",{children:i?"Saving...":"Save Settings"})]})}),e.jsxs("div",{className:"mythology-card p-6 border-red-500/20",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx(I,{className:"w-6 h-6 text-red-400 mr-3"}),e.jsx("h2",{className:"text-xl font-semibold text-red-400",children:"Danger Zone"})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-medium",children:"Delete Account"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Permanently delete your account and all associated data"})]}),e.jsx("button",{onClick:()=>c(!0),className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Delete Account"})]})})]}),v&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsxs(u.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},className:"mythology-card p-6 max-w-md w-full",children:[e.jsx("h3",{className:"text-xl font-semibold text-red-400 mb-4",children:"Confirm Account Deletion"}),e.jsx("p",{className:"text-gray-300 mb-6",children:"Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost."}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{onClick:k,className:"flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors",children:"Yes, Delete Account"}),e.jsx("button",{onClick:()=>c(!1),className:"flex-1 border border-gray-600 text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors",children:"Cancel"})]})]})})]})})};export{F as default};
