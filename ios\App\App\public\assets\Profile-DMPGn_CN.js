import{c as w,e as k,u as S,r as p,z as b,j as e,m as z,X as D,U as C,a as M,C as E,f as L}from"./index-QubfbT_B.js";import{S as P}from"./save-CsSdIJd3.js";import{f as j}from"./format-CBpsKyOP.js";/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],Y=w("pen-line",I),A=()=>{var y;const{updateUserProfile:N}=k(),{user:t,setLoading:x,isLoading:h,setError:g}=S(),[i,o]=p.useState(!1),[d,n]=p.useState({displayName:(t==null?void 0:t.displayName)||"",birthDate:(t==null?void 0:t.birthDate)||"",zodiacSign:(t==null?void 0:t.zodiacSign)||""}),r=a=>{const{name:l,value:c}=a.target;if(n(m=>({...m,[l]:c})),l==="birthDate"&&c){const m=L(new Date(c));n(v=>({...v,zodiacSign:m}))}},u=async()=>{if(t){x(!0),g(null);try{await N({...t,displayName:d.displayName,birthDate:d.birthDate,zodiacSign:d.zodiacSign}),o(!1)}catch(a){g(a.message||"Failed to update profile")}finally{x(!1)}}},f=()=>{n({displayName:(t==null?void 0:t.displayName)||"",birthDate:(t==null?void 0:t.birthDate)||"",zodiacSign:(t==null?void 0:t.zodiacSign)||""}),o(!1)},s=t!=null&&t.zodiacSign?b[t.zodiacSign]:null;return e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs(z.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6},children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"mythology-title text-4xl font-bold mb-4",children:"Your Profile"}),e.jsx("p",{className:"text-xl text-gray-400",children:"Manage your cosmic identity and preferences"})]}),e.jsxs("div",{className:"mythology-card p-8 mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-2xl font-semibold text-mythology-gold",children:"Personal Information"}),i?e.jsxs("div",{className:"flex space-x-2",children:[e.jsxs("button",{onClick:u,disabled:h,className:"mythology-button px-4 py-2 flex items-center space-x-2 disabled:opacity-50",children:[e.jsx(P,{size:16}),e.jsx("span",{children:h?"Saving...":"Save"})]}),e.jsxs("button",{onClick:f,className:"px-4 py-2 border border-gray-600 text-gray-400 rounded-lg hover:bg-gray-600 hover:text-white transition-all duration-300 flex items-center space-x-2",children:[e.jsx(D,{size:16}),e.jsx("span",{children:"Cancel"})]})]}):e.jsxs("button",{onClick:()=>o(!0),className:"flex items-center space-x-2 px-4 py-2 border border-mythology-gold text-mythology-gold rounded-lg hover:bg-mythology-gold hover:text-dark-900 transition-all duration-300",children:[e.jsx(Y,{size:16}),e.jsx("span",{children:"Edit"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsx("div",{className:"w-32 h-32 bg-mythology-gold rounded-full flex items-center justify-center",children:e.jsx(C,{className:"w-16 h-16 text-dark-900"})}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-xl font-semibold text-white",children:(t==null?void 0:t.displayName)||"Cosmic Seeker"}),e.jsx("p",{className:"text-gray-400",children:t==null?void 0:t.email})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Display Name"}),i?e.jsx("input",{type:"text",name:"displayName",value:d.displayName,onChange:r,className:"w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent"}):e.jsx("p",{className:"text-white bg-dark-700 px-4 py-3 rounded-lg",children:(t==null?void 0:t.displayName)||"Not set"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Birth Date"}),i?e.jsx("input",{type:"date",name:"birthDate",value:d.birthDate,onChange:r,className:"w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent"}):e.jsx("p",{className:"text-white bg-dark-700 px-4 py-3 rounded-lg",children:t!=null&&t.birthDate?j(new Date(t.birthDate),"MMMM d, yyyy"):"Not set"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Zodiac Sign"}),i?e.jsxs("select",{name:"zodiacSign",value:d.zodiacSign,onChange:r,className:"w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select your sign"}),Object.entries(b).map(([a,l])=>e.jsxs("option",{value:a,children:[l.symbol," ",l.name]},a))]}):e.jsx("p",{className:"text-white bg-dark-700 px-4 py-3 rounded-lg",children:s?`${s.symbol} ${s.name}`:"Not set"})]})]})]})]}),s&&e.jsxs("div",{className:"mythology-card p-6 mb-8",children:[e.jsx("h2",{className:"text-2xl font-semibold mb-4 text-mythology-gold",children:"Your Zodiac Sign"}),e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx("div",{className:"text-6xl mr-6",children:s.symbol}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-3xl font-semibold text-white mb-2",children:s.name}),e.jsx("p",{className:"text-gray-400 text-lg",children:s.dates})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"text-center p-4 bg-dark-700/50 rounded-lg",children:[e.jsx(M,{className:"w-8 h-8 text-mythology-gold mx-auto mb-2"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Element"}),e.jsx("p",{className:"text-white font-semibold",children:s.element})]}),e.jsxs("div",{className:"text-center p-4 bg-dark-700/50 rounded-lg",children:[e.jsx(E,{className:"w-8 h-8 text-mythology-gold mx-auto mb-2"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Ruling Planet"}),e.jsx("p",{className:"text-white font-semibold",children:s.planet})]}),e.jsxs("div",{className:"text-center p-4 bg-dark-700/50 rounded-lg",children:[e.jsx("div",{className:"w-8 h-8 rounded-full mx-auto mb-2",style:{backgroundColor:s.color}}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Lucky Color"}),e.jsx("p",{className:"text-white font-semibold",children:"Cosmic Hue"})]}),e.jsxs("div",{className:"text-center p-4 bg-dark-700/50 rounded-lg",children:[e.jsx("div",{className:"w-8 h-8 bg-mythology-gold rounded-full mx-auto mb-2 flex items-center justify-center",children:e.jsx("div",{className:"w-4 h-4 bg-white rounded-full"})}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Gemstone"}),e.jsx("p",{className:"text-white font-semibold",children:s.gemstone})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold text-mythology-gold mb-2",children:"Your Traits"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.traits.map((a,l)=>e.jsx("span",{className:"px-3 py-1 bg-mythology-gold/20 text-mythology-gold rounded-full text-sm font-medium",children:a},l))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-semibold text-mythology-gold mb-2",children:"Mythology"}),e.jsx("p",{className:"text-gray-300 leading-relaxed",children:s.mythology})]})]})]}),e.jsxs("div",{className:"mythology-card p-6",children:[e.jsx("h2",{className:"text-2xl font-semibold mb-4 text-mythology-gold",children:"Your Journey"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"text-center p-4 bg-dark-700/50 rounded-lg",children:[e.jsx("div",{className:"text-3xl font-bold text-mythology-gold mb-2",children:t?j(new Date(t.uid),"MMM yyyy"):"N/A"}),e.jsx("p",{className:"text-gray-400",children:"Member Since"})]}),e.jsxs("div",{className:"text-center p-4 bg-dark-700/50 rounded-lg",children:[e.jsx("div",{className:"text-3xl font-bold text-mythology-gold mb-2",children:"0"}),e.jsx("p",{className:"text-gray-400",children:"Readings Generated"})]}),e.jsxs("div",{className:"text-center p-4 bg-dark-700/50 rounded-lg",children:[e.jsx("div",{className:"text-3xl font-bold text-mythology-gold mb-2",children:((y=t==null?void 0:t.preferences)==null?void 0:y.language)==="si"?"සිංහල":"English"}),e.jsx("p",{className:"text-gray-400",children:"Preferred Language"})]})]})]})]})})};export{A as default};
