import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Moon, Star, Compass, RefreshCw, CheckCircle } from 'lucide-react';
import { useStore } from '../store/useStore';
import { zodiacSigns, getDailyInstructions } from '../data/zodiacSigns';
import { format } from 'date-fns';

const DailyGuidance: React.FC = () => {
  const { user, dailyInstructions, setDailyInstructions } = useStore();
  const [selectedInstruction, setSelectedInstruction] = useState<string | null>(null);
  const [completedInstructions, setCompletedInstructions] = useState<string[]>([]);

  const zodiacSign = user?.zodiacSign || 'aries';
  const zodiacInfo = zodiacSigns[zodiacSign];
  const instructions = getDailyInstructions(zodiacSign);

  useEffect(() => {
    // Load completed instructions from localStorage
    const saved = localStorage.getItem(`completed-instructions-${format(new Date(), 'yyyy-MM-dd')}`);
    if (saved) {
      setCompletedInstructions(JSON.parse(saved));
    }
  }, []);

  const handleInstructionComplete = (instruction: string) => {
    const newCompleted = [...completedInstructions, instruction];
    setCompletedInstructions(newCompleted);
    localStorage.setItem(
      `completed-instructions-${format(new Date(), 'yyyy-MM-dd')}`,
      JSON.stringify(newCompleted)
    );
  };

  const handleInstructionUndo = (instruction: string) => {
    const newCompleted = completedInstructions.filter(i => i !== instruction);
    setCompletedInstructions(newCompleted);
    localStorage.setItem(
      `completed-instructions-${format(new Date(), 'yyyy-MM-dd')}`,
      JSON.stringify(newCompleted)
    );
  };

  const getRandomInstruction = () => {
    const availableInstructions = instructions.filter(
      instruction => !completedInstructions.includes(instruction)
    );
    
    if (availableInstructions.length === 0) {
      return instructions[Math.floor(Math.random() * instructions.length)];
    }
    
    return availableInstructions[Math.floor(Math.random() * availableInstructions.length)];
  };

  const handleNewGuidance = () => {
    const newInstruction = getRandomInstruction();
    setSelectedInstruction(newInstruction);
    setDailyInstructions(newInstruction);
  };

  const progressPercentage = (completedInstructions.length / instructions.length) * 100;

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center mb-8">
          <h1 className="mythology-title text-4xl font-bold mb-4">Daily Guidance</h1>
          <p className="text-xl text-gray-400">
            Wisdom tailored to your cosmic nature
          </p>
        </div>

        {/* Zodiac Info Card */}
        <div className="mythology-card p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="text-4xl">{zodiacInfo.symbol}</div>
              <div>
                <h2 className="text-2xl font-semibold text-mythology-gold">{zodiacInfo.name}</h2>
                <p className="text-gray-400">{zodiacInfo.dates}</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-400">Today's Progress</p>
              <div className="flex items-center space-x-2">
                <div className="w-20 h-2 bg-dark-600 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-mythology-gold transition-all duration-500"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
                <span className="text-sm text-mythology-gold font-medium">
                  {Math.round(progressPercentage)}%
                </span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center p-3 bg-dark-700/50 rounded-lg">
              <Sun className="w-6 h-6 text-mythology-gold mx-auto mb-2" />
              <p className="text-gray-400">Element</p>
              <p className="text-white font-medium">{zodiacInfo.element}</p>
            </div>
            <div className="text-center p-3 bg-dark-700/50 rounded-lg">
              <Star className="w-6 h-6 text-mythology-gold mx-auto mb-2" />
              <p className="text-gray-400">Ruling Planet</p>
              <p className="text-white font-medium">{zodiacInfo.planet}</p>
            </div>
            <div className="text-center p-3 bg-dark-700/50 rounded-lg">
              <Moon className="w-6 h-6 text-mythology-gold mx-auto mb-2" />
              <p className="text-gray-400">Gemstone</p>
              <p className="text-white font-medium">{zodiacInfo.gemstone}</p>
            </div>
          </div>
        </div>

        {/* Current Guidance */}
        <div className="mythology-card p-8 mb-8 text-center">
          <Compass className="w-12 h-12 text-mythology-gold mx-auto mb-4" />
          <h2 className="text-2xl font-semibold mb-6 text-mythology-gold">Today's Guidance</h2>
          
          {selectedInstruction || dailyInstructions ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
            >
              <p className="text-xl text-gray-300 leading-relaxed">
                {selectedInstruction || dailyInstructions}
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                {!completedInstructions.includes(selectedInstruction || dailyInstructions || '') ? (
                  <button
                    onClick={() => handleInstructionComplete(selectedInstruction || dailyInstructions || '')}
                    className="mythology-button px-6 py-3 flex items-center space-x-2"
                  >
                    <CheckCircle size={20} />
                    <span>Mark as Completed</span>
                  </button>
                ) : (
                  <button
                    onClick={() => handleInstructionUndo(selectedInstruction || dailyInstructions || '')}
                    className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                  >
                    <CheckCircle size={20} />
                    <span>Completed ✓</span>
                  </button>
                )}
                
                <button
                  onClick={handleNewGuidance}
                  className="px-6 py-3 border border-mythology-gold text-mythology-gold rounded-lg hover:bg-mythology-gold hover:text-dark-900 transition-all duration-300 flex items-center space-x-2"
                >
                  <RefreshCw size={20} />
                  <span>New Guidance</span>
                </button>
              </div>
            </motion.div>
          ) : (
            <div className="space-y-6">
              <p className="text-gray-400">
                Ready to receive your personalized guidance for today?
              </p>
              <button
                onClick={handleNewGuidance}
                className="mythology-button px-8 py-4 text-lg flex items-center space-x-2 mx-auto"
              >
                <Compass size={20} />
                <span>Get Today's Guidance</span>
              </button>
            </div>
          )}
        </div>

        {/* All Instructions */}
        <div className="mythology-card p-6">
          <h2 className="text-2xl font-semibold mb-6 text-mythology-gold">
            {zodiacInfo.name} Guidance Collection
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {instructions.map((instruction, index) => {
              const isCompleted = completedInstructions.includes(instruction);
              const isCurrent = instruction === (selectedInstruction || dailyInstructions);
              
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                    isCurrent
                      ? 'border-mythology-gold bg-mythology-gold/10'
                      : isCompleted
                      ? 'border-green-500/50 bg-green-500/10'
                      : 'border-dark-600 hover:border-dark-500'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <p className={`text-sm leading-relaxed flex-1 ${
                      isCompleted ? 'text-gray-400 line-through' : 'text-gray-300'
                    }`}>
                      {instruction}
                    </p>
                    
                    <div className="ml-3 flex flex-col space-y-2">
                      {isCurrent && (
                        <div className="w-3 h-3 bg-mythology-gold rounded-full animate-pulse"></div>
                      )}
                      {isCompleted && (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-3 flex space-x-2">
                    <button
                      onClick={() => setSelectedInstruction(instruction)}
                      className="text-xs text-mythology-gold hover:text-mythology-bronze transition-colors"
                    >
                      Set as Current
                    </button>
                    
                    {!isCompleted ? (
                      <button
                        onClick={() => handleInstructionComplete(instruction)}
                        className="text-xs text-green-400 hover:text-green-300 transition-colors"
                      >
                        Complete
                      </button>
                    ) : (
                      <button
                        onClick={() => handleInstructionUndo(instruction)}
                        className="text-xs text-gray-400 hover:text-gray-300 transition-colors"
                      >
                        Undo
                      </button>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Traits Section */}
        <div className="mythology-card p-6 mt-8">
          <h2 className="text-2xl font-semibold mb-4 text-mythology-gold">Your {zodiacInfo.name} Traits</h2>
          <div className="flex flex-wrap gap-2">
            {zodiacInfo.traits.map((trait, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-mythology-gold/20 text-mythology-gold rounded-full text-sm font-medium"
              >
                {trait}
              </span>
            ))}
          </div>
          <div className="mt-4">
            <p className="text-gray-300 text-sm leading-relaxed">
              {zodiacInfo.mythology}
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default DailyGuidance;
