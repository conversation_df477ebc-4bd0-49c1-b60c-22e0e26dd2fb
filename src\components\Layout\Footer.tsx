import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, Star, Moon } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-dark-900 border-t border-dark-700 mt-auto">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-mythology-gold rounded-full flex items-center justify-center">
                <span className="text-dark-900 font-bold text-lg">K</span>
              </div>
              <span className="mythology-title text-xl font-bold">Kubera</span>
            </div>
            <p className="text-gray-400 mb-4 max-w-md">
              Discover your destiny through ancient wisdom and modern insights. 
              <PERSON><PERSON><PERSON> brings you personalized horoscopes, future readings, and daily guidance 
              rooted in timeless mythology.
            </p>
            <div className="flex items-center space-x-4">
              <Heart className="text-mythology-gold" size={20} />
              <Star className="text-mythology-gold" size={20} />
              <Moon className="text-mythology-gold" size={20} />
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-mythology-gold font-semibold mb-4">Services</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/horoscope" className="text-gray-400 hover:text-mythology-gold transition-colors">
                  Daily Horoscope
                </Link>
              </li>
              <li>
                <Link to="/future-reading" className="text-gray-400 hover:text-mythology-gold transition-colors">
                  Future Readings
                </Link>
              </li>
              <li>
                <Link to="/daily-guidance" className="text-gray-400 hover:text-mythology-gold transition-colors">
                  Daily Guidance
                </Link>
              </li>
              <li>
                <Link to="/zodiac-compatibility" className="text-gray-400 hover:text-mythology-gold transition-colors">
                  Compatibility
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-mythology-gold font-semibold mb-4">Support</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/about" className="text-gray-400 hover:text-mythology-gold transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-400 hover:text-mythology-gold transition-colors">
                  Contact
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-400 hover:text-mythology-gold transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-gray-400 hover:text-mythology-gold transition-colors">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-dark-700 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            © 2024 Kubera. All rights reserved. Made with{' '}
            <Heart className="inline text-mythology-gold" size={16} />{' '}
            for seekers of wisdom.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
