import{c as p,u as v,r as u,j as e,m as y,C as k,H as w,S as R}from"./index-QubfbT_B.js";import{a as C}from"./apis-tgNVP_Vo.js";import{R as F}from"./refresh-cw-BMeRD5eS.js";import{f as g}from"./format-CBpsKyOP.js";/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S=[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]],z=p("briefcase",S);/**
 * @license lucide-react v0.514.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const M=[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]],o=p("gem",M),I=()=>{const{user:l,futureReadings:i,addFutureReading:j,setLoading:c,isLoading:d,setError:m}=v(),[n,f]=u.useState("general"),[r,x]=u.useState(""),h=[{key:"general",label:"General",icon:e.jsx(o,{size:20}),color:"text-purple-400"},{key:"love",label:"Love & Relationships",icon:e.jsx(w,{size:20}),color:"text-pink-400"},{key:"career",label:"Career & Finance",icon:e.jsx(z,{size:20}),color:"text-green-400"},{key:"spiritual",label:"Spiritual Growth",icon:e.jsx(R,{size:20}),color:"text-mythology-gold"}],b=async()=>{if(l){c(!0),m(null);try{const s={zodiacSign:l.zodiacSign,category:n,question:r.trim()||void 0,preferences:l.preferences},t=await C(s),a={id:Date.now().toString(),userId:l.uid,content:t,date:g(new Date,"yyyy-MM-dd HH:mm:ss"),category:n};j(a),x("")}catch(s){m(s.message||"Failed to generate future reading")}finally{c(!1)}}},N=s=>i.filter(t=>t.category===s);return e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs(y.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6},children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"mythology-title text-4xl font-bold mb-4",children:"Future Readings"}),e.jsx("p",{className:"text-xl text-gray-400",children:"Peer into the cosmic tapestry and discover what awaits you"})]}),e.jsxs("div",{className:"mythology-card p-6 mb-8",children:[e.jsx("h2",{className:"text-2xl font-semibold mb-4 text-mythology-gold",children:"Choose Your Focus"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:h.map(s=>e.jsxs("button",{onClick:()=>f(s.key),className:`p-4 rounded-lg border-2 transition-all duration-300 text-left ${n===s.key?"border-mythology-gold bg-mythology-gold/20":"border-dark-600 hover:border-mythology-gold/50"}`,children:[e.jsx("div",{className:`${s.color} mb-2`,children:s.icon}),e.jsx("h3",{className:"font-semibold text-white mb-1",children:s.label}),e.jsxs("p",{className:"text-sm text-gray-400",children:[s.key==="general"&&"Overall life guidance and insights",s.key==="love"&&"Romantic relationships and connections",s.key==="career"&&"Professional growth and opportunities",s.key==="spiritual"&&"Inner wisdom and enlightenment"]})]},s.key))})]}),e.jsxs("div",{className:"mythology-card p-6 mb-8",children:[e.jsx("h2",{className:"text-2xl font-semibold mb-4 text-mythology-gold",children:"Ask the Oracle"}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx("label",{htmlFor:"question",className:"block text-sm font-medium text-gray-300 mb-2",children:"Specific Question (Optional)"}),e.jsx("textarea",{id:"question",value:r,onChange:s=>x(s.target.value),placeholder:"What would you like to know about your future? The more specific your question, the more focused your reading will be...",className:"w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent resize-none",rows:4,maxLength:500}),e.jsxs("p",{className:"text-sm text-gray-400 mt-2",children:[r.length,"/500 characters"]})]})})]}),e.jsx("div",{className:"text-center mb-8",children:e.jsx("button",{onClick:b,disabled:d,className:"mythology-button px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto",children:d?e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"animate-spin",size:20}),e.jsx("span",{children:"Consulting the Oracle..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(o,{size:20}),e.jsx("span",{children:"Reveal My Future"})]})})}),i.length>0&&e.jsxs("div",{className:"space-y-8",children:[e.jsx("h2",{className:"text-2xl font-semibold text-mythology-gold",children:"Your Readings"}),h.map(s=>{const t=N(s.key);return t.length===0?null:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("h3",{className:"text-xl font-semibold text-white flex items-center space-x-2",children:[e.jsx("span",{className:s.color,children:s.icon}),e.jsx("span",{children:s.label})]}),e.jsx("div",{className:"grid grid-cols-1 gap-6",children:t.slice(0,3).map(a=>e.jsxs(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mythology-card p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:s.color,children:s.icon}),e.jsx("span",{className:"font-semibold text-white",children:s.label})]}),e.jsxs("div",{className:"flex items-center text-sm text-gray-400",children:[e.jsx(k,{size:16,className:"mr-1"}),g(new Date(a.date),"MMM d, yyyy HH:mm")]})]}),e.jsx("div",{className:"prose prose-invert max-w-none",children:e.jsx("p",{className:"text-gray-300 leading-relaxed whitespace-pre-line",children:a.content})})]},a.id))})]},s.key)})]}),i.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(o,{className:"w-16 h-16 text-mythology-gold mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Readings Yet"}),e.jsx("p",{className:"text-gray-400",children:"Generate your first future reading to begin your cosmic journey"})]})]})})};export{I as default};
