import OpenAI from 'openai';
import Replicate from 'replicate';

// OpenAI Configuration
export const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true, // Note: In production, use a backend proxy
});

// Replicate Configuration
export const replicate = new Replicate({
  auth: import.meta.env.VITE_REPLICATE_API_TOKEN,
});

// Google Gemini Configuration
export const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
export const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

// API Helper Functions
export const callGeminiAPI = async (prompt: string) => {
  try {
    const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      }),
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.candidates[0]?.content?.parts[0]?.text || '';
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
};

// Translation function using Gemini
export const translateText = async (text: string, targetLanguage: string) => {
  const prompt = `Translate the following text to ${targetLanguage}. Only return the translated text without any additional explanation:\n\n${text}`;
  return await callGeminiAPI(prompt);
};

// Horoscope generation using OpenAI
export const generateHoroscope = async (zodiacSign: string, period: 'daily' | 'weekly' | 'monthly', _userProfile?: any) => {
  try {
    const prompt = `Generate a ${period} horoscope for ${zodiacSign}. Make it mystical, insightful, and personalized. Include guidance on love, career, health, and spiritual growth. Keep it engaging and positive while being realistic.`;
    
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a wise astrologer and mythology expert. Create engaging, mystical horoscopes that blend ancient wisdom with modern insights."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 300,
      temperature: 0.8,
    });

    return completion.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('Error generating horoscope:', error);
    throw error;
  }
};

// Future reading generation using OpenAI
export const generateFutureReading = async (userInfo: any) => {
  try {
    const prompt = `Based on the following user information, provide a mystical future reading that includes insights about their path ahead, opportunities, challenges, and spiritual guidance: ${JSON.stringify(userInfo)}`;
    
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an ancient oracle with deep mystical knowledge. Provide insightful, encouraging future readings that guide users toward their highest potential."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 400,
      temperature: 0.9,
    });

    return completion.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('Error generating future reading:', error);
    throw error;
  }
};

// Generate mythology-themed images using Replicate
export const generateMythologyImage = async (prompt: string) => {
  try {
    const output = await replicate.run(
      "stability-ai/stable-diffusion:27b93a2413e7f36cd83da926f3656280b2931564ff050bf9575f1fdf9bcd7478",
      {
        input: {
          prompt: `${prompt}, mythology, mystical, ancient, golden light, ethereal, fantasy art style`,
          negative_prompt: "ugly, blurry, low quality, distorted",
          width: 512,
          height: 512,
          num_inference_steps: 20,
          guidance_scale: 7.5,
        }
      }
    );
    
    return output;
  } catch (error) {
    console.error('Error generating image:', error);
    throw error;
  }
};
