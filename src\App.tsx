import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout/Layout';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import ProtectedRoute from './components/ProtectedRoute';

// Lazy load pages for better performance
const Horoscope = React.lazy(() => import('./pages/Horoscope'));
const FutureReading = React.lazy(() => import('./pages/FutureReading'));
const DailyGuidance = React.lazy(() => import('./pages/DailyGuidance'));
const Profile = React.lazy(() => import('./pages/Profile'));
const Settings = React.lazy(() => import('./pages/Settings'));

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <React.Suspense
            fallback={
              <div className="min-h-screen bg-mythology-gradient flex items-center justify-center">
                <div className="loading-spinner w-8 h-8 border-4 border-mythology-gold border-t-transparent rounded-full"></div>
              </div>
            }
          >
            <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />

              {/* Protected Routes with Layout */}
              <Route path="/" element={<Layout />}>
                <Route index element={<Home />} />
                <Route
                  path="horoscope"
                  element={
                    <ProtectedRoute>
                      <Horoscope />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="future-reading"
                  element={
                    <ProtectedRoute>
                      <FutureReading />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="daily-guidance"
                  element={
                    <ProtectedRoute>
                      <DailyGuidance />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="profile"
                  element={
                    <ProtectedRoute>
                      <Profile />
                    </ProtectedRoute>
                  }
                />
                <Route
                  path="settings"
                  element={
                    <ProtectedRoute>
                      <Settings />
                    </ProtectedRoute>
                  }
                />
              </Route>

              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </React.Suspense>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
