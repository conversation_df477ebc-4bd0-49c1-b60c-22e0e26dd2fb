{"name": "kubera", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@capacitor/android": "^7.3.0", "@capacitor/cli": "^7.3.0", "@capacitor/core": "^7.3.0", "@capacitor/ios": "^7.3.0", "@hookform/resolvers": "^5.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.21", "date-fns": "^4.1.0", "firebase": "^11.9.1", "framer-motion": "^12.17.0", "lucide-react": "^0.514.0", "openai": "^5.3.0", "postcss": "^8.5.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-router-dom": "^7.6.2", "replicate": "^1.0.1", "tailwindcss": "^4.1.10", "zod": "^3.25.63", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}