import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Gem, Heart, Briefcase, Sparkles, RefreshCw, Calendar } from 'lucide-react';
import { useStore } from '../store/useStore';
import { generateFutureReading } from '../config/apis';
import { format } from 'date-fns';

const FutureReading: React.FC = () => {
  const { user, futureReadings, addFutureReading, setLoading, isLoading, setError } = useStore();
  const [selectedCategory, setSelectedCategory] = useState('general');
  const [question, setQuestion] = useState('');

  const categories = [
    { key: 'general', label: 'General', icon: <Gem size={20} />, color: 'text-purple-400' },
    { key: 'love', label: 'Love & Relationships', icon: <Heart size={20} />, color: 'text-pink-400' },
    { key: 'career', label: 'Career & Finance', icon: <Briefcase size={20} />, color: 'text-green-400' },
    { key: 'spiritual', label: 'Spiritual Growth', icon: <Sparkles size={20} />, color: 'text-mythology-gold' }
  ];

  const handleGenerateReading = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const userInfo = {
        zodiacSign: user.zodiacSign,
        category: selectedCategory,
        question: question.trim() || undefined,
        preferences: user.preferences
      };

      const content = await generateFutureReading(userInfo);
      
      const newReading = {
        id: Date.now().toString(),
        userId: user.uid,
        content,
        date: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
        category: selectedCategory,
      };

      addFutureReading(newReading);
      setQuestion(''); // Clear the question after generating
    } catch (error: any) {
      setError(error.message || 'Failed to generate future reading');
    } finally {
      setLoading(false);
    }
  };

  const getCategoryReadings = (category: string) => {
    return futureReadings.filter(reading => reading.category === category);
  };

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center mb-8">
          <h1 className="mythology-title text-4xl font-bold mb-4">Future Readings</h1>
          <p className="text-xl text-gray-400">
            Peer into the cosmic tapestry and discover what awaits you
          </p>
        </div>

        {/* Category Selection */}
        <div className="mythology-card p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-mythology-gold">Choose Your Focus</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {categories.map((category) => (
              <button
                key={category.key}
                onClick={() => setSelectedCategory(category.key)}
                className={`p-4 rounded-lg border-2 transition-all duration-300 text-left ${
                  selectedCategory === category.key
                    ? 'border-mythology-gold bg-mythology-gold/20'
                    : 'border-dark-600 hover:border-mythology-gold/50'
                }`}
              >
                <div className={`${category.color} mb-2`}>
                  {category.icon}
                </div>
                <h3 className="font-semibold text-white mb-1">{category.label}</h3>
                <p className="text-sm text-gray-400">
                  {category.key === 'general' && 'Overall life guidance and insights'}
                  {category.key === 'love' && 'Romantic relationships and connections'}
                  {category.key === 'career' && 'Professional growth and opportunities'}
                  {category.key === 'spiritual' && 'Inner wisdom and enlightenment'}
                </p>
              </button>
            ))}
          </div>
        </div>

        {/* Question Input */}
        <div className="mythology-card p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4 text-mythology-gold">Ask the Oracle</h2>
          <div className="space-y-4">
            <div>
              <label htmlFor="question" className="block text-sm font-medium text-gray-300 mb-2">
                Specific Question (Optional)
              </label>
              <textarea
                id="question"
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                placeholder="What would you like to know about your future? The more specific your question, the more focused your reading will be..."
                className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent resize-none"
                rows={4}
                maxLength={500}
              />
              <p className="text-sm text-gray-400 mt-2">
                {question.length}/500 characters
              </p>
            </div>
          </div>
        </div>

        {/* Generate Button */}
        <div className="text-center mb-8">
          <button
            onClick={handleGenerateReading}
            disabled={isLoading}
            className="mythology-button px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto"
          >
            {isLoading ? (
              <>
                <RefreshCw className="animate-spin" size={20} />
                <span>Consulting the Oracle...</span>
              </>
            ) : (
              <>
                <Crystal size={20} />
                <span>Reveal My Future</span>
              </>
            )}
          </button>
        </div>

        {/* Recent Readings */}
        {futureReadings.length > 0 && (
          <div className="space-y-8">
            <h2 className="text-2xl font-semibold text-mythology-gold">Your Readings</h2>
            
            {categories.map((category) => {
              const categoryReadings = getCategoryReadings(category.key);
              if (categoryReadings.length === 0) return null;

              return (
                <div key={category.key} className="space-y-4">
                  <h3 className="text-xl font-semibold text-white flex items-center space-x-2">
                    <span className={category.color}>{category.icon}</span>
                    <span>{category.label}</span>
                  </h3>
                  
                  <div className="grid grid-cols-1 gap-6">
                    {categoryReadings.slice(0, 3).map((reading) => (
                      <motion.div
                        key={reading.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mythology-card p-6"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-2">
                            <span className={category.color}>{category.icon}</span>
                            <span className="font-semibold text-white">{category.label}</span>
                          </div>
                          <div className="flex items-center text-sm text-gray-400">
                            <Calendar size={16} className="mr-1" />
                            {format(new Date(reading.date), 'MMM d, yyyy HH:mm')}
                          </div>
                        </div>
                        
                        <div className="prose prose-invert max-w-none">
                          <p className="text-gray-300 leading-relaxed whitespace-pre-line">
                            {reading.content}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Empty State */}
        {futureReadings.length === 0 && (
          <div className="text-center py-12">
            <Crystal className="w-16 h-16 text-mythology-gold mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No Readings Yet</h3>
            <p className="text-gray-400">
              Generate your first future reading to begin your cosmic journey
            </p>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default FutureReading;
