import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { User, Calendar, Star, Edit3, Save, X } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useStore } from '../store/useStore';
import { zodiacSigns, getZodiacByDate } from '../data/zodiacSigns';
import { format } from 'date-fns';

const Profile: React.FC = () => {
  const { updateUserProfile } = useAuth();
  const { user, setLoading, isLoading, setError } = useStore();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    displayName: user?.displayName || '',
    birthDate: user?.birthDate || '',
    zodiacSign: user?.zodiacSign || ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Auto-calculate zodiac sign when birth date changes
    if (name === 'birthDate' && value) {
      const zodiac = getZodiacByDate(new Date(value));
      setFormData(prev => ({
        ...prev,
        zodiacSign: zodiac
      }));
    }
  };

  const handleSave = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      await updateUserProfile({
        ...user,
        displayName: formData.displayName,
        birthDate: formData.birthDate,
        zodiacSign: formData.zodiacSign
      });
      setIsEditing(false);
    } catch (error: any) {
      setError(error.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      displayName: user?.displayName || '',
      birthDate: user?.birthDate || '',
      zodiacSign: user?.zodiacSign || ''
    });
    setIsEditing(false);
  };

  const zodiacInfo = user?.zodiacSign ? zodiacSigns[user.zodiacSign] : null;

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center mb-8">
          <h1 className="mythology-title text-4xl font-bold mb-4">Your Profile</h1>
          <p className="text-xl text-gray-400">
            Manage your cosmic identity and preferences
          </p>
        </div>

        {/* Profile Card */}
        <div className="mythology-card p-8 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-semibold text-mythology-gold">Personal Information</h2>
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="flex items-center space-x-2 px-4 py-2 border border-mythology-gold text-mythology-gold rounded-lg hover:bg-mythology-gold hover:text-dark-900 transition-all duration-300"
              >
                <Edit3 size={16} />
                <span>Edit</span>
              </button>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="mythology-button px-4 py-2 flex items-center space-x-2 disabled:opacity-50"
                >
                  <Save size={16} />
                  <span>{isLoading ? 'Saving...' : 'Save'}</span>
                </button>
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 border border-gray-600 text-gray-400 rounded-lg hover:bg-gray-600 hover:text-white transition-all duration-300 flex items-center space-x-2"
                >
                  <X size={16} />
                  <span>Cancel</span>
                </button>
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Avatar Section */}
            <div className="flex flex-col items-center space-y-4">
              <div className="w-32 h-32 bg-mythology-gold rounded-full flex items-center justify-center">
                <User className="w-16 h-16 text-dark-900" />
              </div>
              <div className="text-center">
                <h3 className="text-xl font-semibold text-white">
                  {user?.displayName || 'Cosmic Seeker'}
                </h3>
                <p className="text-gray-400">{user?.email}</p>
              </div>
            </div>

            {/* Form Section */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Display Name
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    name="displayName"
                    value={formData.displayName}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent"
                  />
                ) : (
                  <p className="text-white bg-dark-700 px-4 py-3 rounded-lg">
                    {user?.displayName || 'Not set'}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Birth Date
                </label>
                {isEditing ? (
                  <input
                    type="date"
                    name="birthDate"
                    value={formData.birthDate}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent"
                  />
                ) : (
                  <p className="text-white bg-dark-700 px-4 py-3 rounded-lg">
                    {user?.birthDate ? format(new Date(user.birthDate), 'MMMM d, yyyy') : 'Not set'}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Zodiac Sign
                </label>
                {isEditing ? (
                  <select
                    name="zodiacSign"
                    value={formData.zodiacSign}
                    onChange={handleChange}
                    className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent"
                  >
                    <option value="">Select your sign</option>
                    {Object.entries(zodiacSigns).map(([key, sign]) => (
                      <option key={key} value={key}>
                        {sign.symbol} {sign.name}
                      </option>
                    ))}
                  </select>
                ) : (
                  <p className="text-white bg-dark-700 px-4 py-3 rounded-lg">
                    {zodiacInfo ? `${zodiacInfo.symbol} ${zodiacInfo.name}` : 'Not set'}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Zodiac Information */}
        {zodiacInfo && (
          <div className="mythology-card p-6 mb-8">
            <h2 className="text-2xl font-semibold mb-4 text-mythology-gold">Your Zodiac Sign</h2>
            
            <div className="flex items-center mb-6">
              <div className="text-6xl mr-6">{zodiacInfo.symbol}</div>
              <div>
                <h3 className="text-3xl font-semibold text-white mb-2">{zodiacInfo.name}</h3>
                <p className="text-gray-400 text-lg">{zodiacInfo.dates}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-dark-700/50 rounded-lg">
                <Star className="w-8 h-8 text-mythology-gold mx-auto mb-2" />
                <p className="text-gray-400 text-sm">Element</p>
                <p className="text-white font-semibold">{zodiacInfo.element}</p>
              </div>
              <div className="text-center p-4 bg-dark-700/50 rounded-lg">
                <Calendar className="w-8 h-8 text-mythology-gold mx-auto mb-2" />
                <p className="text-gray-400 text-sm">Ruling Planet</p>
                <p className="text-white font-semibold">{zodiacInfo.planet}</p>
              </div>
              <div className="text-center p-4 bg-dark-700/50 rounded-lg">
                <div 
                  className="w-8 h-8 rounded-full mx-auto mb-2"
                  style={{ backgroundColor: zodiacInfo.color }}
                ></div>
                <p className="text-gray-400 text-sm">Lucky Color</p>
                <p className="text-white font-semibold">Cosmic Hue</p>
              </div>
              <div className="text-center p-4 bg-dark-700/50 rounded-lg">
                <div className="w-8 h-8 bg-mythology-gold rounded-full mx-auto mb-2 flex items-center justify-center">
                  <div className="w-4 h-4 bg-white rounded-full"></div>
                </div>
                <p className="text-gray-400 text-sm">Gemstone</p>
                <p className="text-white font-semibold">{zodiacInfo.gemstone}</p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="text-lg font-semibold text-mythology-gold mb-2">Your Traits</h4>
                <div className="flex flex-wrap gap-2">
                  {zodiacInfo.traits.map((trait, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-mythology-gold/20 text-mythology-gold rounded-full text-sm font-medium"
                    >
                      {trait}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-lg font-semibold text-mythology-gold mb-2">Mythology</h4>
                <p className="text-gray-300 leading-relaxed">{zodiacInfo.mythology}</p>
              </div>
            </div>
          </div>
        )}

        {/* Account Statistics */}
        <div className="mythology-card p-6">
          <h2 className="text-2xl font-semibold mb-4 text-mythology-gold">Your Journey</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-dark-700/50 rounded-lg">
              <div className="text-3xl font-bold text-mythology-gold mb-2">
                {user ? format(new Date(user.uid), 'MMM yyyy') : 'N/A'}
              </div>
              <p className="text-gray-400">Member Since</p>
            </div>
            <div className="text-center p-4 bg-dark-700/50 rounded-lg">
              <div className="text-3xl font-bold text-mythology-gold mb-2">
                {/* This would come from your store */}
                0
              </div>
              <p className="text-gray-400">Readings Generated</p>
            </div>
            <div className="text-center p-4 bg-dark-700/50 rounded-lg">
              <div className="text-3xl font-bold text-mythology-gold mb-2">
                {user?.preferences?.language === 'si' ? 'සිංහල' : 'English'}
              </div>
              <p className="text-gray-400">Preferred Language</p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default Profile;
