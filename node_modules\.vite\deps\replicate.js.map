{"version": 3, "sources": ["../../replicate/lib/error.js", "../../replicate/lib/identifier.js", "../../replicate/lib/files.js", "../../replicate/lib/util.js", "../../replicate/vendor/eventsource-parser/stream.js", "../../replicate/vendor/streams-text-encoding/text-decoder-stream.js", "../../replicate/lib/stream.js", "../../replicate/lib/accounts.js", "../../replicate/lib/collections.js", "../../replicate/lib/deployments.js", "../../replicate/lib/hardware.js", "../../replicate/lib/models.js", "../../replicate/lib/predictions.js", "../../replicate/lib/trainings.js", "../../replicate/lib/webhooks.js", "../../replicate/package.json", "../../replicate/index.js"], "sourcesContent": ["/**\n * A representation of an API error.\n */\nclass ApiError extends Error {\n  /**\n   * Creates a representation of an API error.\n   *\n   * @param {string} message - Error message\n   * @param {Request} request - HTTP request\n   * @param {Response} response - HTTP response\n   * @returns {ApiError} - An instance of ApiError\n   */\n  constructor(message, request, response) {\n    super(message);\n    this.name = \"ApiError\";\n    this.request = request;\n    this.response = response;\n  }\n}\n\nmodule.exports = ApiError;\n", "/*\n * A reference to a model version in the format `owner/name` or `owner/name:version`.\n */\nclass ModelVersionIdentifier {\n  /*\n   * @param {string} Required. The model owner.\n   * @param {string} Required. The model name.\n   * @param {string} The model version.\n   */\n  constructor(owner, name, version = null) {\n    this.owner = owner;\n    this.name = name;\n    this.version = version;\n  }\n\n  /*\n   * Parse a reference to a model version\n   *\n   * @param {string}\n   * @returns {ModelVersionIdentifier}\n   * @throws {Error} If the reference is invalid.\n   */\n  static parse(ref) {\n    const match = ref.match(\n      /^(?<owner>[^/]+)\\/(?<name>[^/:]+)(:(?<version>.+))?$/\n    );\n    if (!match) {\n      throw new Error(\n        `Invalid reference to model version: ${ref}. Expected format: owner/name or owner/name:version`\n      );\n    }\n\n    const { owner, name, version } = match.groups;\n\n    return new ModelVersionIdentifier(owner, name, version);\n  }\n}\n\nmodule.exports = ModelVersionIdentifier;\n", "/**\n * Create a file\n *\n * @param {object} file - Required. The file object.\n * @param {object} metadata - Optional. User-provided metadata associated with the file.\n * @returns {Promise<object>} - Resolves with the file data\n */\nasync function createFile(file, metadata = {}) {\n  const form = new FormData();\n\n  let filename;\n  let blob;\n  if (file instanceof Blob) {\n    filename = file.name || `blob_${Date.now()}`;\n    blob = file;\n  } else if (Buffer.isBuffer(file)) {\n    filename = `buffer_${Date.now()}`;\n    const bytes = new Uint8Array(file);\n    blob = new Blob([bytes], {\n      type: \"application/octet-stream\",\n      name: filename,\n    });\n  } else {\n    throw new Error(\"Invalid file argument, must be a Blob, File or Buffer\");\n  }\n\n  form.append(\"content\", blob, filename);\n  form.append(\n    \"metadata\",\n    new Blob([JSON.stringify(metadata)], { type: \"application/json\" })\n  );\n\n  const response = await this.request(\"/files\", {\n    method: \"POST\",\n    data: form,\n    headers: {\n      \"Content-Type\": \"multipart/form-data\",\n    },\n  });\n\n  return response.json();\n}\n\n/**\n * List all files\n *\n * @returns {Promise<object>} - Resolves with the files data\n */\nasync function listFiles() {\n  const response = await this.request(\"/files\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Get a file\n *\n * @param {string} file_id - Required. The ID of the file.\n * @returns {Promise<object>} - Resolves with the file data\n */\nasync function getFile(file_id) {\n  const response = await this.request(`/files/${file_id}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Delete a file\n *\n * @param {string} file_id - Required. The ID of the file.\n * @returns {Promise<boolean>} - Resolves with true if the file was deleted\n */\nasync function deleteFile(file_id) {\n  const response = await this.request(`/files/${file_id}`, {\n    method: \"DELETE\",\n  });\n\n  return response.status === 204;\n}\n\nmodule.exports = {\n  create: createFile,\n  list: listFiles,\n  get: getFile,\n  delete: deleteFile,\n};\n", "const ApiError = require(\"./error\");\nconst { create: createFile } = require(\"./files\");\n\n/**\n * @see {@link validateWebhook}\n * @overload\n * @param {object} requestData - The request data\n * @param {string} requestData.id - The webhook ID header from the incoming request.\n * @param {string} requestData.timestamp - The webhook timestamp header from the incoming request.\n * @param {string} requestData.body - The raw body of the incoming webhook request.\n * @param {string} requestData.secret - The webhook secret, obtained from `replicate.webhooks.defaul.secret` method.\n * @param {string} requestData.signature - The webhook signature header from the incoming request, comprising one or more space-delimited signatures.\n */\n\n/**\n * @see {@link validateWebhook}\n * @overload\n * @param {object} requestData - The request object\n * @param {object} requestData.headers - The request headers\n * @param {string} requestData.headers[\"webhook-id\"] - The webhook ID header from the incoming request\n * @param {string} requestData.headers[\"webhook-timestamp\"] - The webhook timestamp header from the incoming request\n * @param {string} requestData.headers[\"webhook-signature\"] - The webhook signature header from the incoming request, comprising one or more space-delimited signatures\n * @param {string} requestData.body - The raw body of the incoming webhook request\n * @param {string} secret - The webhook secret, obtained from `replicate.webhooks.defaul.secret` method\n */\n\n/**\n * Validate a webhook signature\n *\n * @returns {Promise<boolean>} - True if the signature is valid\n * @throws {Error} - If the request is missing required headers, body, or secret\n */\nasync function validateWebhook(requestData, secret) {\n  let { id, timestamp, body, signature } = requestData;\n  const signingSecret = secret || requestData.secret;\n\n  if (requestData && requestData.headers && requestData.body) {\n    if (typeof requestData.headers.get === \"function\") {\n      // Headers object (e.g. Fetch API Headers)\n      id = requestData.headers.get(\"webhook-id\");\n      timestamp = requestData.headers.get(\"webhook-timestamp\");\n      signature = requestData.headers.get(\"webhook-signature\");\n    } else {\n      // Plain object with header key-value pairs\n      id = requestData.headers[\"webhook-id\"];\n      timestamp = requestData.headers[\"webhook-timestamp\"];\n      signature = requestData.headers[\"webhook-signature\"];\n    }\n    body = requestData.body;\n  }\n\n  if (body instanceof ReadableStream || body.readable) {\n    try {\n      body = await new Response(body).text();\n    } catch (err) {\n      throw new Error(`Error reading body: ${err.message}`);\n    }\n  } else if (isTypedArray(body)) {\n    body = await new Blob([body]).text();\n  } else if (typeof body === \"object\") {\n    body = JSON.stringify(body);\n  } else if (typeof body !== \"string\") {\n    throw new Error(\"Invalid body type\");\n  }\n\n  if (!id || !timestamp || !signature) {\n    throw new Error(\"Missing required webhook headers\");\n  }\n\n  if (!body) {\n    throw new Error(\"Missing required body\");\n  }\n\n  if (!signingSecret) {\n    throw new Error(\"Missing required secret\");\n  }\n\n  const signedContent = `${id}.${timestamp}.${body}`;\n\n  const computedSignature = await createHMACSHA256(\n    signingSecret.split(\"_\").pop(),\n    signedContent\n  );\n\n  const expectedSignatures = signature\n    .split(\" \")\n    .map((sig) => sig.split(\",\")[1]);\n\n  return expectedSignatures.some(\n    (expectedSignature) => expectedSignature === computedSignature\n  );\n}\n\n/**\n * @param {string} secret - base64 encoded string\n * @param {string} data - text body of request\n */\nasync function createHMACSHA256(secret, data) {\n  const encoder = new TextEncoder();\n  let crypto = globalThis.crypto;\n\n  // In Node 18 the `crypto` global is behind a --no-experimental-global-webcrypto flag\n  if (typeof crypto === \"undefined\" && typeof require === \"function\") {\n    // NOTE: Webpack (primarily as it's used by Next.js) and perhaps some\n    // other bundlers do not currently support the `node` protocol and will\n    // error if it's found in the source. Other platforms like CloudFlare\n    // will only support requires when using the node protocol.\n    //\n    // As this line is purely to support Node 18.x we make an indirect request\n    // to the require function which fools Webpack...\n    //\n    // We may be able to remove this in future as it looks like Webpack is getting\n    // support for requiring using the `node:` protocol.\n    // See: https://github.com/webpack/webpack/issues/18277\n    crypto = require.call(null, \"node:crypto\").webcrypto;\n  }\n\n  const key = await crypto.subtle.importKey(\n    \"raw\",\n    base64ToBytes(secret),\n    { name: \"HMAC\", hash: \"SHA-256\" },\n    false,\n    [\"sign\"]\n  );\n\n  const signature = await crypto.subtle.sign(\"HMAC\", key, encoder.encode(data));\n  return bytesToBase64(signature);\n}\n\n/**\n * Convert a base64 encoded string into bytes.\n *\n * @param {string} the base64 encoded string\n * @return {Uint8Array}\n *\n * Two functions for encoding/decoding base64 strings using web standards. Not\n * intended to be used to encode/decode arbitrary string data.\n * See: https://developer.mozilla.org/en-US/docs/Glossary/Base64#javascript_support\n * See: https://stackoverflow.com/a/31621532\n *\n * Performance might take a hit because of the conversion to string and then to binary,\n * if this is the case we might want to look at an alternative solution.\n * See: https://jsben.ch/wnaZC\n */\nfunction base64ToBytes(base64) {\n  return Uint8Array.from(atob(base64), (m) => m.codePointAt(0));\n}\n\n/**\n * Convert a base64 encoded string into bytes.\n *\n * See {@link base64ToBytes} for caveats.\n *\n * @param {Uint8Array | ArrayBuffer} the base64 encoded string\n * @return {string}\n */\nfunction bytesToBase64(bytes) {\n  return btoa(String.fromCharCode.apply(null, new Uint8Array(bytes)));\n}\n\n/**\n * Automatically retry a request if it fails with an appropriate status code.\n *\n * A GET request is retried if it fails with a 429 or 5xx status code.\n * A non-GET request is retried only if it fails with a 429 status code.\n *\n * If the response sets a Retry-After header,\n * the request is retried after the number of seconds specified in the header.\n * Otherwise, the request is retried after the specified interval,\n * with exponential backoff and jitter.\n *\n * @param {Function} request - A function that returns a Promise that resolves with a Response object\n * @param {object} options\n * @param {Function} [options.shouldRetry] - A function that returns true if the request should be retried\n * @param {number} [options.maxRetries] - Maximum number of retries. Defaults to 5\n * @param {number} [options.interval] - Interval between retries in milliseconds. Defaults to 500\n * @returns {Promise<Response>} - Resolves with the response object\n * @throws {ApiError} If the request failed\n */\nasync function withAutomaticRetries(request, options = {}) {\n  const shouldRetry = options.shouldRetry || (() => false);\n  const maxRetries = options.maxRetries || 5;\n  const interval = options.interval || 500;\n  const jitter = options.jitter || 100;\n\n  // eslint-disable-next-line no-promise-executor-return\n  const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\n\n  let attempts = 0;\n  do {\n    let delay = interval * 2 ** attempts + Math.random() * jitter;\n\n    /* eslint-disable no-await-in-loop */\n    try {\n      const response = await request();\n      if (response.ok || !shouldRetry(response)) {\n        return response;\n      }\n    } catch (error) {\n      if (error instanceof ApiError) {\n        const retryAfter = error.response.headers.get(\"Retry-After\");\n        if (retryAfter) {\n          if (!Number.isInteger(retryAfter)) {\n            // Retry-After is a date\n            const date = new Date(retryAfter);\n            if (!Number.isNaN(date.getTime())) {\n              delay = date.getTime() - new Date().getTime();\n            }\n          } else {\n            // Retry-After is a number of seconds\n            delay = retryAfter * 1000;\n          }\n        }\n      }\n    }\n\n    if (Number.isInteger(maxRetries) && maxRetries > 0) {\n      if (Number.isInteger(delay) && delay > 0) {\n        await sleep(interval * 2 ** (options.maxRetries - maxRetries));\n      }\n      attempts += 1;\n    }\n  } while (attempts < maxRetries);\n\n  return request();\n}\n\n/**\n * Walks the inputs and, for any File or Blob, tries to upload it to Replicate\n * and replaces the input with the URL of the uploaded file.\n *\n * @param {Replicate} client - The client used to upload the file\n * @param {object} inputs - The inputs to transform\n * @param {\"default\" | \"upload\" | \"data-uri\"} strategy - Whether to upload files to Replicate, encode as dataURIs or try both.\n * @returns {object} - The transformed inputs\n * @throws {ApiError} If the request to upload the file fails\n */\nasync function transformFileInputs(client, inputs, strategy) {\n  switch (strategy) {\n    case \"data-uri\":\n      return await transformFileInputsToBase64EncodedDataURIs(client, inputs);\n    case \"upload\":\n      return await transformFileInputsToReplicateFileURLs(client, inputs);\n    case \"default\":\n      try {\n        return await transformFileInputsToReplicateFileURLs(client, inputs);\n      } catch (error) {\n        if (\n          error instanceof ApiError &&\n          error.response.status >= 400 &&\n          error.response.status < 500\n        ) {\n          throw error;\n        }\n        return await transformFileInputsToBase64EncodedDataURIs(inputs);\n      }\n    default:\n      throw new Error(`Unexpected file upload strategy: ${strategy}`);\n  }\n}\n\n/**\n * Walks the inputs and, for any File or Blob, tries to upload it to Replicate\n * and replaces the input with the URL of the uploaded file.\n *\n * @param {Replicate} client - The client used to upload the file\n * @param {object} inputs - The inputs to transform\n * @returns {object} - The transformed inputs\n * @throws {ApiError} If the request to upload the file fails\n */\nasync function transformFileInputsToReplicateFileURLs(client, inputs) {\n  return await transform(inputs, async (value) => {\n    if (value instanceof Blob || value instanceof Buffer) {\n      const file = await createFile.call(client, value);\n      return file.urls.get;\n    }\n\n    return value;\n  });\n}\n\nconst MAX_DATA_URI_SIZE = 10_000_000;\n\n/**\n * Walks the inputs and transforms any binary data found into a\n * base64-encoded data URI.\n *\n * @param {object} inputs - The inputs to transform\n * @returns {object} - The transformed inputs\n * @throws {Error} If the size of inputs exceeds a given threshould set by MAX_DATA_URI_SIZE\n */\nasync function transformFileInputsToBase64EncodedDataURIs(inputs) {\n  let totalBytes = 0;\n  return await transform(inputs, async (value) => {\n    let buffer;\n    let mime;\n\n    if (value instanceof Blob) {\n      // Currently we use a NodeJS only API for base64 encoding, as\n      // we move to support the browser we could support either using\n      // btoa (which does string encoding), the FileReader API or\n      // a JavaScript implenentation like base64-js.\n      // See: https://developer.mozilla.org/en-US/docs/Glossary/Base64\n      // See: https://github.com/beatgammit/base64-js\n      buffer = await value.arrayBuffer();\n      mime = value.type;\n    } else if (isTypedArray(value)) {\n      buffer = value;\n    } else {\n      return value;\n    }\n\n    totalBytes += buffer.byteLength;\n    if (totalBytes > MAX_DATA_URI_SIZE) {\n      throw new Error(\n        `Combined filesize of prediction ${totalBytes} bytes exceeds 10mb limit for inline encoding, please provide URLs instead`\n      );\n    }\n\n    const data = bytesToBase64(buffer);\n    mime = mime || \"application/octet-stream\";\n\n    return `data:${mime};base64,${data}`;\n  });\n}\n\n// Walk a JavaScript object and transform the leaf values.\nasync function transform(value, mapper) {\n  if (Array.isArray(value)) {\n    const copy = [];\n    for (const val of value) {\n      const transformed = await transform(val, mapper);\n      copy.push(transformed);\n    }\n    return copy;\n  }\n\n  if (isPlainObject(value)) {\n    const copy = {};\n    for (const key of Object.keys(value)) {\n      copy[key] = await transform(value[key], mapper);\n    }\n    return copy;\n  }\n\n  return await mapper(value);\n}\n\nfunction isTypedArray(arr) {\n  return (\n    arr instanceof Int8Array ||\n    arr instanceof Int16Array ||\n    arr instanceof Int32Array ||\n    arr instanceof Uint8Array ||\n    arr instanceof Uint8ClampedArray ||\n    arr instanceof Uint16Array ||\n    arr instanceof Uint32Array ||\n    arr instanceof Float32Array ||\n    arr instanceof Float64Array\n  );\n}\n\n// Test for a plain JS object.\n// Source: lodash.isPlainObject\nfunction isPlainObject(value) {\n  const isObjectLike = typeof value === \"object\" && value !== null;\n  if (!isObjectLike || String(value) !== \"[object Object]\") {\n    return false;\n  }\n  const proto = Object.getPrototypeOf(value);\n  if (proto === null) {\n    return true;\n  }\n  const Ctor =\n    Object.prototype.hasOwnProperty.call(proto, \"constructor\") &&\n    proto.constructor;\n  return (\n    typeof Ctor === \"function\" &&\n    Ctor instanceof Ctor &&\n    Function.prototype.toString.call(Ctor) ===\n      Function.prototype.toString.call(Object)\n  );\n}\n\n/**\n * Parse progress from prediction logs.\n *\n * This function supports log statements in the following format,\n * which are generated by https://github.com/tqdm/tqdm and similar libraries:\n *\n * ```\n * 76%|████████████████████████████         | 7568/10000 [00:33<00:10, 229.00it/s]\n * ```\n *\n * @example\n * const progress = parseProgressFromLogs(\"76%|████████████████████████████         | 7568/10000 [00:33<00:10, 229.00it/s]\");\n * console.log(progress);\n * // {\n * //   percentage: 0.76,\n * //   current: 7568,\n * //   total: 10000,\n * // }\n *\n * @param {object|string} input - A prediction object or string.\n * @returns {(object|null)} - An object with the percentage, current, and total, or null if no progress can be parsed.\n */\nfunction parseProgressFromLogs(input) {\n  const logs = typeof input === \"object\" && input.logs ? input.logs : input;\n  if (!logs || typeof logs !== \"string\") {\n    return null;\n  }\n\n  const pattern = /^\\s*(\\d+)%\\s*\\|.+?\\|\\s*(\\d+)\\/(\\d+)/;\n  const lines = logs.split(\"\\n\").reverse();\n\n  for (const line of lines) {\n    const matches = line.match(pattern);\n\n    if (matches && matches.length === 4) {\n      return {\n        percentage: parseInt(matches[1], 10) / 100,\n        current: parseInt(matches[2], 10),\n        total: parseInt(matches[3], 10),\n      };\n    }\n  }\n\n  return null;\n}\n\n/**\n * Helper to make any `ReadableStream` iterable, this is supported\n * by most server runtimes but browsers still haven't implemented\n * it yet.\n * See: https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream#browser_compatibility\n *\n * @template T\n * @param {ReadableStream<T>} stream an instance of a `ReadableStream`\n * @yields {T} a chunk/event from the stream\n */\nasync function* streamAsyncIterator(stream) {\n  const reader = stream.getReader();\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) return;\n      yield value;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nmodule.exports = {\n  transform,\n  transformFileInputs,\n  validateWebhook,\n  withAutomaticRetries,\n  parseProgressFromLogs,\n  streamAsyncIterator,\n};\n", "// Source: https://github.com/rexxars/eventsource-parser/tree/v1.1.2\n//\n// MIT License\n//\n// Copyright (c) 2024 <PERSON><PERSON><PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if ((from && typeof from === \"object\") || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, {\n          get: () => from[key],\n          enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable,\n        });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) =>\n  __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// /input.ts\nvar input_exports = {};\n__export(input_exports, {\n  EventSourceParserStream: () => EventSourceParserStream,\n});\nmodule.exports = __toCommonJS(input_exports);\n\n// http-url:https://unpkg.com/eventsource-parser@1.1.2/dist/index.js\nfunction createParser(onParse) {\n  let isFirstChunk;\n  let buffer;\n  let startingPosition;\n  let startingFieldLength;\n  let eventId;\n  let eventName;\n  let data;\n  reset();\n  return {\n    feed,\n    reset,\n  };\n  function reset() {\n    isFirstChunk = true;\n    buffer = \"\";\n    startingPosition = 0;\n    startingFieldLength = -1;\n    eventId = void 0;\n    eventName = void 0;\n    data = \"\";\n  }\n  function feed(chunk) {\n    buffer = buffer ? buffer + chunk : chunk;\n    if (isFirstChunk && hasBom(buffer)) {\n      buffer = buffer.slice(BOM.length);\n    }\n    isFirstChunk = false;\n    const length = buffer.length;\n    let position = 0;\n    let discardTrailingNewline = false;\n    while (position < length) {\n      if (discardTrailingNewline) {\n        if (buffer[position] === \"\\n\") {\n          ++position;\n        }\n        discardTrailingNewline = false;\n      }\n      let lineLength = -1;\n      let fieldLength = startingFieldLength;\n      let character;\n      for (\n        let index = startingPosition;\n        lineLength < 0 && index < length;\n        ++index\n      ) {\n        character = buffer[index];\n        if (character === \":\" && fieldLength < 0) {\n          fieldLength = index - position;\n        } else if (character === \"\\r\") {\n          discardTrailingNewline = true;\n          lineLength = index - position;\n        } else if (character === \"\\n\") {\n          lineLength = index - position;\n        }\n      }\n      if (lineLength < 0) {\n        startingPosition = length - position;\n        startingFieldLength = fieldLength;\n        break;\n      } else {\n        startingPosition = 0;\n        startingFieldLength = -1;\n      }\n      parseEventStreamLine(buffer, position, fieldLength, lineLength);\n      position += lineLength + 1;\n    }\n    if (position === length) {\n      buffer = \"\";\n    } else if (position > 0) {\n      buffer = buffer.slice(position);\n    }\n  }\n  function parseEventStreamLine(lineBuffer, index, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        onParse({\n          type: \"event\",\n          id: eventId,\n          event: eventName || void 0,\n          data: data.slice(0, -1),\n          // remove trailing newline\n        });\n        data = \"\";\n        eventId = void 0;\n      }\n      eventName = void 0;\n      return;\n    }\n    const noValue = fieldLength < 0;\n    const field = lineBuffer.slice(\n      index,\n      index + (noValue ? lineLength : fieldLength)\n    );\n    let step = 0;\n    if (noValue) {\n      step = lineLength;\n    } else if (lineBuffer[index + fieldLength + 1] === \" \") {\n      step = fieldLength + 2;\n    } else {\n      step = fieldLength + 1;\n    }\n    const position = index + step;\n    const valueLength = lineLength - step;\n    const value = lineBuffer.slice(position, position + valueLength).toString();\n    if (field === \"data\") {\n      data += value ? \"\".concat(value, \"\\n\") : \"\\n\";\n    } else if (field === \"event\") {\n      eventName = value;\n    } else if (field === \"id\" && !value.includes(\"\\0\")) {\n      eventId = value;\n    } else if (field === \"retry\") {\n      const retry = parseInt(value, 10);\n      if (!Number.isNaN(retry)) {\n        onParse({\n          type: \"reconnect-interval\",\n          value: retry,\n        });\n      }\n    }\n  }\n}\nvar BOM = [239, 187, 191];\nfunction hasBom(buffer) {\n  return BOM.every((charCode, index) => buffer.charCodeAt(index) === charCode);\n}\n\n// http-url:https://unpkg.com/eventsource-parser@1.1.2/dist/stream.js\nvar EventSourceParserStream = class extends TransformStream {\n  constructor() {\n    let parser;\n    super({\n      start(controller) {\n        parser = createParser((event) => {\n          if (event.type === \"event\") {\n            controller.enqueue(event);\n          }\n        });\n      },\n      transform(chunk) {\n        parser.feed(chunk);\n      },\n    });\n  }\n};\n", "// Adapted from https://github.com/stardazed/sd-streams\n//\n// MIT License\n//\n// Copyright (c) 2018-Present @zenmumbler\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n// SOFTWARE.\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// /input.ts\nvar input_exports = {};\n__export(input_exports, {\n  TextDecoderStream: () => TextDecoderStream\n});\nmodule.exports = __toCommonJS(input_exports);\n\n// http-url:https://unpkg.com/@stardazed/streams-text-encoding@1.0.2/dist/sd-streams-text-encoding.esm.js\nvar decDecoder = Symbol(\"decDecoder\");\nvar decTransform = Symbol(\"decTransform\");\nvar TextDecodeTransformer = class {\n  constructor(decoder) {\n    this.decoder_ = decoder;\n  }\n  transform(chunk, controller) {\n    if (!(chunk instanceof ArrayBuffer || ArrayBuffer.isView(chunk))) {\n      throw new TypeError(\"Input data must be a BufferSource\");\n    }\n    const text = this.decoder_.decode(chunk, { stream: true });\n    if (text.length !== 0) {\n      controller.enqueue(text);\n    }\n  }\n  flush(controller) {\n    const text = this.decoder_.decode();\n    if (text.length !== 0) {\n      controller.enqueue(text);\n    }\n  }\n};\nvar TextDecoderStream = class {\n  constructor(label, options) {\n    const decoder = new TextDecoder(label || \"utf-8\", options || {});\n    this[decDecoder] = decoder;\n    this[decTransform] = new TransformStream(new TextDecodeTransformer(decoder));\n  }\n  get encoding() {\n    return this[decDecoder].encoding;\n  }\n  get fatal() {\n    return this[decDecoder].fatal;\n  }\n  get ignoreBOM() {\n    return this[decDecoder].ignoreBOM;\n  }\n  get readable() {\n    return this[decTransform].readable;\n  }\n  get writable() {\n    return this[decTransform].writable;\n  }\n};\nvar encEncoder = Symbol(\"encEncoder\");\nvar encTransform = Symbol(\"encTransform\");\n", "// Attempt to use readable-stream if available, attempt to use the built-in stream module.\n\nconst ApiError = require(\"./error\");\nconst { streamAsyncIterator } = require(\"./util\");\nconst {\n  EventSourceParserStream,\n} = require(\"../vendor/eventsource-parser/stream\");\nconst { TextDecoderStream } =\n  typeof globalThis.TextDecoderStream === \"undefined\"\n    ? require(\"../vendor/streams-text-encoding/text-decoder-stream\")\n    : globalThis;\n\n/**\n * A server-sent event.\n */\nclass ServerSentEvent {\n  /**\n   * Create a new server-sent event.\n   *\n   * @param {string} event The event name.\n   * @param {string} data The event data.\n   * @param {string} id The event ID.\n   * @param {number} retry The retry time.\n   */\n  constructor(event, data, id, retry) {\n    this.event = event;\n    this.data = data;\n    this.id = id;\n    this.retry = retry;\n  }\n\n  /**\n   * Convert the event to a string.\n   */\n  toString() {\n    if (this.event === \"output\") {\n      return this.data;\n    }\n\n    return \"\";\n  }\n}\n\n/**\n * Create a new stream of server-sent events.\n *\n * @param {object} config\n * @param {string} config.url The URL to connect to.\n * @param {typeof fetch} [config.fetch] The URL to connect to.\n * @param {object} [config.options] The EventSource options.\n * @param {boolean} [config.options.useFileOutput] Whether to use the file output stream.\n * @returns {ReadableStream<ServerSentEvent> & AsyncIterable<ServerSentEvent>}\n */\nfunction createReadableStream({ url, fetch, options = {} }) {\n  const { useFileOutput = true, headers = {}, ...initOptions } = options;\n\n  return new ReadableStream({\n    async start(controller) {\n      const init = {\n        ...initOptions,\n        headers: {\n          ...headers,\n          Accept: \"text/event-stream\",\n        },\n      };\n      const response = await fetch(url, init);\n\n      if (!response.ok) {\n        const text = await response.text();\n        const request = new Request(url, init);\n        controller.error(\n          new ApiError(\n            `Request to ${url} failed with status ${response.status}: ${text}`,\n            request,\n            response\n          )\n        );\n      }\n\n      const stream = response.body\n        .pipeThrough(new TextDecoderStream())\n        .pipeThrough(new EventSourceParserStream());\n\n      for await (const event of streamAsyncIterator(stream)) {\n        if (event.event === \"error\") {\n          controller.error(new Error(event.data));\n          break;\n        }\n\n        let data = event.data;\n        if (\n          useFileOutput &&\n          typeof data === \"string\" &&\n          (data.startsWith(\"https:\") || data.startsWith(\"data:\"))\n        ) {\n          data = createFileOutput({ data, fetch });\n        }\n        controller.enqueue(new ServerSentEvent(event.event, data, event.id));\n\n        if (event.event === \"done\") {\n          break;\n        }\n      }\n\n      controller.close();\n    },\n  });\n}\n\n/**\n * Create a new readable stream for an output file\n * created by running a Replicate model.\n *\n * @param {object} config\n * @param {string} config.url The URL to connect to.\n * @param {typeof fetch} [config.fetch] The fetch function.\n * @returns {ReadableStream<Uint8Array>}\n */\nfunction createFileOutput({ url, fetch }) {\n  let type = \"application/octet-stream\";\n\n  class FileOutput extends ReadableStream {\n    async blob() {\n      const chunks = [];\n      for await (const chunk of this) {\n        chunks.push(chunk);\n      }\n      return new Blob(chunks, { type });\n    }\n\n    url() {\n      return new URL(url);\n    }\n\n    toString() {\n      return url;\n    }\n  }\n\n  return new FileOutput({\n    async start(controller) {\n      const response = await fetch(url);\n\n      if (!response.ok) {\n        const text = await response.text();\n        const request = new Request(url, init);\n        controller.error(\n          new ApiError(\n            `Request to ${url} failed with status ${response.status}: ${text}`,\n            request,\n            response\n          )\n        );\n      }\n\n      if (response.headers.get(\"Content-Type\")) {\n        type = response.headers.get(\"Content-Type\");\n      }\n\n      try {\n        for await (const chunk of streamAsyncIterator(response.body)) {\n          controller.enqueue(chunk);\n        }\n        controller.close();\n      } catch (err) {\n        controller.error(err);\n      }\n    },\n  });\n}\n\nmodule.exports = {\n  createFileOutput,\n  createReadableStream,\n  ServerSentEvent,\n};\n", "/**\n * Get the current account\n *\n * @returns {Promise<object>} Resolves with the current account\n */\nasync function getCurrentAccount() {\n  const response = await this.request(\"/account\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  current: getCurrentAccount,\n};\n", "/**\n * Fetch a model collection\n *\n * @param {string} collection_slug - Required. The slug of the collection. See http://replicate.com/collections\n * @returns {Promise<object>} - Resolves with the collection data\n */\nasync function getCollection(collection_slug) {\n  const response = await this.request(`/collections/${collection_slug}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Fetch a list of model collections\n *\n * @returns {Promise<object>} - Resolves with the collections data\n */\nasync function listCollections() {\n  const response = await this.request(\"/collections\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = { get: getCollection, list: listCollections };\n", "const { transformFileInputs } = require(\"./util\");\n\n/**\n * Create a new prediction with a deployment\n *\n * @param {string} deployment_owner - Required. The username of the user or organization who owns the deployment\n * @param {string} deployment_name - Required. The name of the deployment\n * @param {object} options\n * @param {object} options.input - Required. An object with the model inputs\n * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n * @param {boolean|integer} [options.wait] - Whether to wait until the prediction is completed before returning. If an integer is provided, it will wait for that many seconds. Defaults to false\n * @returns {Promise<object>} Resolves with the created prediction data\n */\nasync function createPrediction(deployment_owner, deployment_name, options) {\n  const { input, wait, ...data } = options;\n\n  if (data.webhook) {\n    try {\n      // eslint-disable-next-line no-new\n      new URL(data.webhook);\n    } catch (err) {\n      throw new Error(\"Invalid webhook URL\");\n    }\n  }\n\n  const headers = {};\n  if (wait) {\n    if (typeof wait === \"number\") {\n      const n = Math.max(1, Math.ceil(Number(wait)) || 1);\n      headers[\"Prefer\"] = `wait=${n}`;\n    } else {\n      headers[\"Prefer\"] = \"wait\";\n    }\n  }\n\n  const response = await this.request(\n    `/deployments/${deployment_owner}/${deployment_name}/predictions`,\n    {\n      method: \"POST\",\n      headers,\n      data: {\n        ...data,\n        input: await transformFileInputs(\n          this,\n          input,\n          this.fileEncodingStrategy\n        ),\n      },\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Get a deployment\n *\n * @param {string} deployment_owner - Required. The username of the user or organization who owns the deployment\n * @param {string} deployment_name - Required. The name of the deployment\n * @returns {Promise<object>} Resolves with the deployment data\n */\nasync function getDeployment(deployment_owner, deployment_name) {\n  const response = await this.request(\n    `/deployments/${deployment_owner}/${deployment_name}`,\n    {\n      method: \"GET\",\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * @typedef {Object} DeploymentCreateRequest - Request body for `deployments.create`\n * @property {string} name - the name of the deployment\n * @property {string} model - the full name of the model that you want to deploy e.g. stability-ai/sdxl\n * @property {string} version - the 64-character string ID of the model version that you want to deploy\n * @property {string} hardware - the SKU for the hardware used to run the model, via `replicate.hardware.list()`\n * @property {number} min_instances - the minimum number of instances for scaling\n * @property {number} max_instances - the maximum number of instances for scaling\n */\n\n/**\n * Create a deployment\n *\n * @param {DeploymentCreateRequest} config - Required. The deployment config.\n * @returns {Promise<object>} Resolves with the deployment data\n */\nasync function createDeployment(deployment_config) {\n  const response = await this.request(\"/deployments\", {\n    method: \"POST\",\n    data: deployment_config,\n  });\n\n  return response.json();\n}\n\n/**\n * @typedef {Object} DeploymentUpdateRequest - Request body for `deployments.update`\n * @property {string} version - the 64-character string ID of the model version that you want to deploy\n * @property {string} hardware - the SKU for the hardware used to run the model, via `replicate.hardware.list()`\n * @property {number} min_instances - the minimum number of instances for scaling\n * @property {number} max_instances - the maximum number of instances for scaling\n */\n\n/**\n * Update an existing deployment\n *\n * @param {string} deployment_owner - Required. The username of the user or organization who owns the deployment\n * @param {string} deployment_name - Required. The name of the deployment\n * @param {DeploymentUpdateRequest} deployment_config - Required. The deployment changes.\n * @returns {Promise<object>} Resolves with the deployment data\n */\nasync function updateDeployment(\n  deployment_owner,\n  deployment_name,\n  deployment_config\n) {\n  const response = await this.request(\n    `/deployments/${deployment_owner}/${deployment_name}`,\n    {\n      method: \"PATCH\",\n      data: deployment_config,\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Delete a deployment\n *\n * @param {string} deployment_owner - Required. The username of the user or organization who owns the deployment\n * @param {string} deployment_name - Required. The name of the deployment\n * @returns {Promise<boolean>} Resolves with true if the deployment was deleted\n */\nasync function deleteDeployment(deployment_owner, deployment_name) {\n  const response = await this.request(\n    `/deployments/${deployment_owner}/${deployment_name}`,\n    {\n      method: \"DELETE\",\n    }\n  );\n\n  return response.status === 204;\n}\n\n/**\n * List all deployments\n *\n * @returns {Promise<object>} - Resolves with a page of deployments\n */\nasync function listDeployments() {\n  const response = await this.request(\"/deployments\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  predictions: {\n    create: createPrediction,\n  },\n  get: getDeployment,\n  create: createDeployment,\n  update: updateDeployment,\n  list: listDeployments,\n  delete: deleteDeployment,\n};\n", "/**\n * List hardware\n *\n * @returns {Promise<object[]>} Resolves with the array of hardware\n */\nasync function listHardware() {\n  const response = await this.request(\"/hardware\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  list: listHardware,\n};\n", "/**\n * Get information about a model\n *\n * @param {string} model_owner - Required. The name of the user or organization that owns the model\n * @param {string} model_name - Required. The name of the model\n * @returns {Promise<object>} Resolves with the model data\n */\nasync function getModel(model_owner, model_name) {\n  const response = await this.request(`/models/${model_owner}/${model_name}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * List model versions\n *\n * @param {string} model_owner - Required. The name of the user or organization that owns the model\n * @param {string} model_name - Required. The name of the model\n * @returns {Promise<object>} Resolves with the list of model versions\n */\nasync function listModelVersions(model_owner, model_name) {\n  const response = await this.request(\n    `/models/${model_owner}/${model_name}/versions`,\n    {\n      method: \"GET\",\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Get a specific model version\n *\n * @param {string} model_owner - Required. The name of the user or organization that owns the model\n * @param {string} model_name - Required. The name of the model\n * @param {string} version_id - Required. The model version\n * @returns {Promise<object>} Resolves with the model version data\n */\nasync function getModelVersion(model_owner, model_name, version_id) {\n  const response = await this.request(\n    `/models/${model_owner}/${model_name}/versions/${version_id}`,\n    {\n      method: \"GET\",\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * List all public models\n *\n * @returns {Promise<object>} Resolves with the model version data\n */\nasync function listModels() {\n  const response = await this.request(\"/models\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Create a new model\n *\n * @param {string} model_owner - Required. The name of the user or organization that will own the model. This must be the same as the user or organization that is making the API request. In other words, the API token used in the request must belong to this user or organization.\n * @param {string} model_name - Required. The name of the model. This must be unique among all models owned by the user or organization.\n * @param {object} options\n * @param {(\"public\"|\"private\")} options.visibility - Required. Whether the model should be public or private. A public model can be viewed and run by anyone, whereas a private model can be viewed and run only by the user or organization members that own the model.\n * @param {string} options.hardware - Required. The SKU for the hardware used to run the model. Possible values can be found by calling `Replicate.hardware.list()`.\n * @param {string} options.description - A description of the model.\n * @param {string} options.github_url - A URL for the model's source code on GitHub.\n * @param {string} options.paper_url - A URL for the model's paper.\n * @param {string} options.license_url - A URL for the model's license.\n * @param {string} options.cover_image_url - A URL for the model's cover image. This should be an image file.\n * @returns {Promise<object>} Resolves with the model version data\n */\nasync function createModel(model_owner, model_name, options) {\n  const data = { owner: model_owner, name: model_name, ...options };\n\n  const response = await this.request(\"/models\", {\n    method: \"POST\",\n    data,\n  });\n\n  return response.json();\n}\n\n/**\n * Search for public models\n *\n * @param {string} query - The search query\n * @returns {Promise<object>} Resolves with a page of models matching the search query\n */\nasync function search(query) {\n  const response = await this.request(\"/models\", {\n    method: \"QUERY\",\n    headers: {\n      \"Content-Type\": \"text/plain\",\n    },\n    data: query,\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  get: getModel,\n  list: listModels,\n  create: createModel,\n  versions: { list: listModelVersions, get: getModelVersion },\n  search,\n};\n", "const { transformFileInputs } = require(\"./util\");\n\n/**\n * Create a new prediction\n *\n * @param {object} options\n * @param {string} options.model - The model.\n * @param {string} options.version - The model version.\n * @param {object} options.input - Required. An object with the model inputs\n * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n * @param {boolean|integer} [options.wait] - Whether to wait until the prediction is completed before returning. If an integer is provided, it will wait for that many seconds. Defaults to false\n * @returns {Promise<object>} Resolves with the created prediction\n */\nasync function createPrediction(options) {\n  const { model, version, input, wait, ...data } = options;\n\n  if (data.webhook) {\n    try {\n      // eslint-disable-next-line no-new\n      new URL(data.webhook);\n    } catch (err) {\n      throw new Error(\"Invalid webhook URL\");\n    }\n  }\n\n  const headers = {};\n  if (wait) {\n    if (typeof wait === \"number\") {\n      const n = Math.max(1, Math.ceil(Number(wait)) || 1);\n      headers[\"Prefer\"] = `wait=${n}`;\n    } else {\n      headers[\"Prefer\"] = \"wait\";\n    }\n  }\n\n  let response;\n  if (version) {\n    response = await this.request(\"/predictions\", {\n      method: \"POST\",\n      headers,\n      data: {\n        ...data,\n        input: await transformFileInputs(\n          this,\n          input,\n          this.fileEncodingStrategy\n        ),\n        version,\n      },\n    });\n  } else if (model) {\n    response = await this.request(`/models/${model}/predictions`, {\n      method: \"POST\",\n      headers,\n      data: {\n        ...data,\n        input: await transformFileInputs(\n          this,\n          input,\n          this.fileEncodingStrategy\n        ),\n      },\n    });\n  } else {\n    throw new Error(\"Either model or version must be specified\");\n  }\n\n  return response.json();\n}\n\n/**\n * Fetch a prediction by ID\n *\n * @param {number} prediction_id - Required. The prediction ID\n * @returns {Promise<object>} Resolves with the prediction data\n */\nasync function getPrediction(prediction_id) {\n  const response = await this.request(`/predictions/${prediction_id}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Cancel a prediction by ID\n *\n * @param {string} prediction_id - Required. The training ID\n * @returns {Promise<object>} Resolves with the data for the training\n */\nasync function cancelPrediction(prediction_id) {\n  const response = await this.request(`/predictions/${prediction_id}/cancel`, {\n    method: \"POST\",\n  });\n\n  return response.json();\n}\n\n/**\n * List all predictions\n *\n * @returns {Promise<object>} - Resolves with a page of predictions\n */\nasync function listPredictions() {\n  const response = await this.request(\"/predictions\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  create: createPrediction,\n  get: getPrediction,\n  cancel: cancelPrediction,\n  list: listPredictions,\n};\n", "/**\n * Create a new training\n *\n * @param {string} model_owner - Required. The username of the user or organization who owns the model\n * @param {string} model_name - Required. The name of the model\n * @param {string} version_id - Required. The version ID\n * @param {object} options\n * @param {string} options.destination - Required. The destination for the trained version in the form \"{username}/{model_name}\"\n * @param {object} options.input - Required. An object with the model inputs\n * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the training updates\n * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n * @returns {Promise<object>} Resolves with the data for the created training\n */\nasync function createTraining(model_owner, model_name, version_id, options) {\n  const { ...data } = options;\n\n  if (data.webhook) {\n    try {\n      // eslint-disable-next-line no-new\n      new URL(data.webhook);\n    } catch (err) {\n      throw new Error(\"Invalid webhook URL\");\n    }\n  }\n\n  const response = await this.request(\n    `/models/${model_owner}/${model_name}/versions/${version_id}/trainings`,\n    {\n      method: \"POST\",\n      data,\n    }\n  );\n\n  return response.json();\n}\n\n/**\n * Fetch a training by ID\n *\n * @param {string} training_id - Required. The training ID\n * @returns {Promise<object>} Resolves with the data for the training\n */\nasync function getTraining(training_id) {\n  const response = await this.request(`/trainings/${training_id}`, {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\n/**\n * Cancel a training by ID\n *\n * @param {string} training_id - Required. The training ID\n * @returns {Promise<object>} Resolves with the data for the training\n */\nasync function cancelTraining(training_id) {\n  const response = await this.request(`/trainings/${training_id}/cancel`, {\n    method: \"POST\",\n  });\n\n  return response.json();\n}\n\n/**\n * List all trainings\n *\n * @returns {Promise<object>} - Resolves with a page of trainings\n */\nasync function listTrainings() {\n  const response = await this.request(\"/trainings\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  create: createTraining,\n  get: getTraining,\n  cancel: cancelTraining,\n  list: listTrainings,\n};\n", "/**\n * Get the default webhook signing secret\n *\n * @returns {Promise<object>} Resolves with the signing secret for the default webhook\n */\nasync function getDefaultWebhookSecret() {\n  const response = await this.request(\"/webhooks/default/secret\", {\n    method: \"GET\",\n  });\n\n  return response.json();\n}\n\nmodule.exports = {\n  default: {\n    secret: {\n      get: getDefaultWebhookSecret,\n    },\n  },\n};\n", "{\n  \"name\": \"replicate\",\n  \"version\": \"1.0.1\",\n  \"description\": \"JavaScript client for Replicate\",\n  \"repository\": \"github:replicate/replicate-javascript\",\n  \"homepage\": \"https://github.com/replicate/replicate-javascript#readme\",\n  \"bugs\": \"https://github.com/replicate/replicate-javascript/issues\",\n  \"license\": \"Apache-2.0\",\n  \"main\": \"index.js\",\n  \"type\": \"commonjs\",\n  \"types\": \"index.d.ts\",\n  \"files\": [\n    \"CONTRIBUTING.md\",\n    \"LICENSE\",\n    \"README.md\",\n    \"index.d.ts\",\n    \"index.js\",\n    \"lib/**/*.js\",\n    \"vendor/**/*\",\n    \"package.json\"\n  ],\n  \"engines\": {\n    \"node\": \">=18.0.0\",\n    \"npm\": \">=7.19.0\",\n    \"git\": \">=2.11.0\",\n    \"yarn\": \">=1.7.0\"\n  },\n  \"scripts\": {\n    \"check\": \"tsc\",\n    \"format\": \"biome format . --write\",\n    \"lint-biome\": \"biome lint .\",\n    \"lint-publint\": \"publint\",\n    \"lint\": \"npm run lint-biome && npm run lint-publint\",\n    \"test\": \"jest\"\n  },\n  \"optionalDependencies\": {\n    \"readable-stream\": \">=4.0.0\"\n  },\n  \"devDependencies\": {\n    \"@biomejs/biome\": \"^1.4.1\",\n    \"@types/jest\": \"^29.5.3\",\n    \"@typescript-eslint/eslint-plugin\": \"^5.56.0\",\n    \"cross-fetch\": \"^3.1.5\",\n    \"jest\": \"^29.7.0\",\n    \"nock\": \"^14.0.0-beta.6\",\n    \"publint\": \"^0.2.7\",\n    \"ts-jest\": \"^29.1.0\",\n    \"typescript\": \"^5.0.2\"\n  }\n}\n", "const ApiError = require(\"./lib/error\");\nconst ModelVersionIdentifier = require(\"./lib/identifier\");\nconst { createReadableStream, createFileOutput } = require(\"./lib/stream\");\nconst {\n  transform,\n  withAutomaticRetries,\n  validateWebhook,\n  parseProgressFromLogs,\n  streamAsyncIterator,\n} = require(\"./lib/util\");\n\nconst accounts = require(\"./lib/accounts\");\nconst collections = require(\"./lib/collections\");\nconst deployments = require(\"./lib/deployments\");\nconst files = require(\"./lib/files\");\nconst hardware = require(\"./lib/hardware\");\nconst models = require(\"./lib/models\");\nconst predictions = require(\"./lib/predictions\");\nconst trainings = require(\"./lib/trainings\");\nconst webhooks = require(\"./lib/webhooks\");\n\nconst packageJSON = require(\"./package.json\");\n\n/**\n * Replicate API client library\n *\n * @see https://replicate.com/docs/reference/http\n * @example\n * // Create a new Replicate API client instance\n * const Replicate = require(\"replicate\");\n * const replicate = new Replicate({\n *     // get your token from https://replicate.com/account\n *     auth: process.env.REPLICATE_API_TOKEN,\n *     userAgent: \"my-app/1.2.3\"\n * });\n *\n * // Run a model and await the result:\n * const model = 'owner/model:version-id'\n * const input = {text: 'Hello, world!'}\n * const output = await replicate.run(model, { input });\n */\nclass Replicate {\n  /**\n   * Create a new Replicate API client instance.\n   *\n   * @param {object} options - Configuration options for the client\n   * @param {string} options.auth - API access token. Defaults to the `REPLICATE_API_TOKEN` environment variable.\n   * @param {string} options.userAgent - Identifier of your app\n   * @param {string} [options.baseUrl] - Defaults to https://api.replicate.com/v1\n   * @param {Function} [options.fetch] - Fetch function to use. Defaults to `globalThis.fetch`\n   * @param {boolean} [options.useFileOutput] - Set to `false` to disable `FileOutput` objects from `run` instead of URLs, defaults to true.\n   * @param {\"default\" | \"upload\" | \"data-uri\"} [options.fileEncodingStrategy] - Determines the file encoding strategy to use\n   */\n  constructor(options = {}) {\n    this.auth =\n      options.auth ||\n      (typeof process !== \"undefined\" ? process.env.REPLICATE_API_TOKEN : null);\n    this.userAgent =\n      options.userAgent || `replicate-javascript/${packageJSON.version}`;\n    this.baseUrl = options.baseUrl || \"https://api.replicate.com/v1\";\n    this.fetch = options.fetch || globalThis.fetch;\n    this.fileEncodingStrategy = options.fileEncodingStrategy || \"default\";\n    this.useFileOutput = options.useFileOutput === false ? false : true;\n\n    this.accounts = {\n      current: accounts.current.bind(this),\n    };\n\n    this.collections = {\n      list: collections.list.bind(this),\n      get: collections.get.bind(this),\n    };\n\n    this.deployments = {\n      get: deployments.get.bind(this),\n      create: deployments.create.bind(this),\n      update: deployments.update.bind(this),\n      delete: deployments.delete.bind(this),\n      list: deployments.list.bind(this),\n      predictions: {\n        create: deployments.predictions.create.bind(this),\n      },\n    };\n\n    this.files = {\n      create: files.create.bind(this),\n      get: files.get.bind(this),\n      list: files.list.bind(this),\n      delete: files.delete.bind(this),\n    };\n\n    this.hardware = {\n      list: hardware.list.bind(this),\n    };\n\n    this.models = {\n      get: models.get.bind(this),\n      list: models.list.bind(this),\n      create: models.create.bind(this),\n      versions: {\n        list: models.versions.list.bind(this),\n        get: models.versions.get.bind(this),\n      },\n      search: models.search.bind(this),\n    };\n\n    this.predictions = {\n      create: predictions.create.bind(this),\n      get: predictions.get.bind(this),\n      cancel: predictions.cancel.bind(this),\n      list: predictions.list.bind(this),\n    };\n\n    this.trainings = {\n      create: trainings.create.bind(this),\n      get: trainings.get.bind(this),\n      cancel: trainings.cancel.bind(this),\n      list: trainings.list.bind(this),\n    };\n\n    this.webhooks = {\n      default: {\n        secret: {\n          get: webhooks.default.secret.get.bind(this),\n        },\n      },\n    };\n  }\n\n  /**\n   * Run a model and wait for its output.\n   *\n   * @param {string} ref - Required. The model version identifier in the format \"owner/name\" or \"owner/name:version\"\n   * @param {object} options\n   * @param {object} options.input - Required. An object with the model inputs\n   * @param {{mode: \"block\", timeout?: number, interval?: number} | {mode: \"poll\", interval?: number }} [options.wait] - Options for waiting for the prediction to finish. If `wait` is explicitly true, the function will block and wait for the prediction to finish.\n   * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n   * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n   * @param {AbortSignal} [options.signal] - AbortSignal to cancel the prediction\n   * @param {Function} [progress] - Callback function that receives the prediction object as it's updated. The function is called when the prediction is created, each time its updated while polling for completion, and when it's completed.\n   * @throws {Error} If the reference is invalid\n   * @throws {Error} If the prediction failed\n   * @returns {Promise<object>} - Resolves with the output of running the model\n   */\n  async run(ref, options, progress) {\n    const { wait = { mode: \"block\" }, signal, ...data } = options;\n\n    const identifier = ModelVersionIdentifier.parse(ref);\n\n    let prediction;\n    if (identifier.version) {\n      prediction = await this.predictions.create({\n        ...data,\n        version: identifier.version,\n        wait: wait.mode === \"block\" ? wait.timeout ?? true : false,\n      });\n    } else if (identifier.owner && identifier.name) {\n      prediction = await this.predictions.create({\n        ...data,\n        model: `${identifier.owner}/${identifier.name}`,\n        wait: wait.mode === \"block\" ? wait.timeout ?? true : false,\n      });\n    } else {\n      throw new Error(\"Invalid model version identifier\");\n    }\n\n    // Call progress callback with the initial prediction object\n    if (progress) {\n      progress(prediction);\n    }\n\n    const isDone = wait.mode === \"block\" && prediction.status !== \"starting\";\n    if (!isDone) {\n      prediction = await this.wait(\n        prediction,\n        { interval: wait.mode === \"poll\" ? wait.interval : undefined },\n        async (updatedPrediction) => {\n          // Call progress callback with the updated prediction object\n          if (progress) {\n            progress(updatedPrediction);\n          }\n\n          // We handle the cancel later in the function.\n          if (signal && signal.aborted) {\n            return true; // stop polling\n          }\n\n          return false; // continue polling\n        }\n      );\n    }\n\n    if (signal && signal.aborted) {\n      prediction = await this.predictions.cancel(prediction.id);\n    }\n\n    // Call progress callback with the completed prediction object\n    if (progress) {\n      progress(prediction);\n    }\n\n    if (prediction.status === \"failed\") {\n      throw new Error(`Prediction failed: ${prediction.error}`);\n    }\n\n    return transform(prediction.output, (value) => {\n      if (\n        typeof value === \"string\" &&\n        (value.startsWith(\"https:\") || value.startsWith(\"data:\"))\n      ) {\n        return this.useFileOutput\n          ? createFileOutput({ url: value, fetch: this.fetch })\n          : value;\n      }\n      return value;\n    });\n  }\n\n  /**\n   * Make a request to the Replicate API.\n   *\n   * @param {string} route - REST API endpoint path\n   * @param {object} options - Request parameters\n   * @param {string} [options.method] - HTTP method. Defaults to GET\n   * @param {object} [options.params] - Query parameters\n   * @param {object|Headers} [options.headers] - HTTP headers\n   * @param {object} [options.data] - Body parameters\n   * @returns {Promise<Response>} - Resolves with the response object\n   * @throws {ApiError} If the request failed\n   */\n  async request(route, options) {\n    const { auth, baseUrl, userAgent } = this;\n\n    let url;\n    if (route instanceof URL) {\n      url = route;\n    } else {\n      url = new URL(\n        route.startsWith(\"/\") ? route.slice(1) : route,\n        baseUrl.endsWith(\"/\") ? baseUrl : `${baseUrl}/`\n      );\n    }\n\n    const { method = \"GET\", params = {}, data } = options;\n\n    for (const [key, value] of Object.entries(params)) {\n      url.searchParams.append(key, value);\n    }\n\n    const headers = {\n      \"Content-Type\": \"application/json\",\n      \"User-Agent\": userAgent,\n    };\n    if (auth) {\n      headers[\"Authorization\"] = `Bearer ${auth}`;\n    }\n    if (options.headers) {\n      for (const [key, value] of Object.entries(options.headers)) {\n        headers[key] = value;\n      }\n    }\n\n    let body = undefined;\n    if (data instanceof FormData) {\n      body = data;\n      // biome-ignore lint/performance/noDelete:\n      delete headers[\"Content-Type\"]; // Use automatic content type header\n    } else if (data) {\n      body = JSON.stringify(data);\n    }\n\n    const init = {\n      method,\n      headers,\n      body,\n    };\n\n    const shouldRetry =\n      method === \"GET\"\n        ? (response) => response.status === 429 || response.status >= 500\n        : (response) => response.status === 429;\n\n    // Workaround to fix `TypeError: Illegal invocation` error in Cloudflare Workers\n    // https://github.com/replicate/replicate-javascript/issues/134\n    const _fetch = this.fetch; // eslint-disable-line no-underscore-dangle\n    const response = await withAutomaticRetries(async () => _fetch(url, init), {\n      shouldRetry,\n    });\n\n    if (!response.ok) {\n      const request = new Request(url, init);\n      const responseText = await response.text();\n      throw new ApiError(\n        `Request to ${url} failed with status ${response.status} ${response.statusText}: ${responseText}.`,\n        request,\n        response\n      );\n    }\n\n    return response;\n  }\n\n  /**\n   * Stream a model and wait for its output.\n   *\n   * @param {string} identifier - Required. The model version identifier in the format \"{owner}/{name}:{version}\"\n   * @param {object} options\n   * @param {object} options.input - Required. An object with the model inputs\n   * @param {string} [options.webhook] - An HTTPS URL for receiving a webhook when the prediction has new output\n   * @param {string[]} [options.webhook_events_filter] - You can change which events trigger webhook requests by specifying webhook events (`start`|`output`|`logs`|`completed`)\n   * @param {AbortSignal} [options.signal] - AbortSignal to cancel the prediction\n   * @throws {Error} If the prediction failed\n   * @yields {ServerSentEvent} Each streamed event from the prediction\n   */\n  async *stream(ref, options) {\n    const { wait, signal, ...data } = options;\n\n    const identifier = ModelVersionIdentifier.parse(ref);\n\n    let prediction;\n    if (identifier.version) {\n      prediction = await this.predictions.create({\n        ...data,\n        version: identifier.version,\n      });\n    } else if (identifier.owner && identifier.name) {\n      prediction = await this.predictions.create({\n        ...data,\n        model: `${identifier.owner}/${identifier.name}`,\n      });\n    } else {\n      throw new Error(\"Invalid model version identifier\");\n    }\n\n    if (prediction.urls && prediction.urls.stream) {\n      const stream = createReadableStream({\n        url: prediction.urls.stream,\n        fetch: this.fetch,\n        ...(signal ? { options: { signal } } : {}),\n      });\n\n      yield* streamAsyncIterator(stream);\n    } else {\n      throw new Error(\"Prediction does not support streaming\");\n    }\n  }\n\n  /**\n   * Paginate through a list of results.\n   *\n   * @generator\n   * @example\n   * for await (const page of replicate.paginate(replicate.predictions.list) {\n   *    console.log(page);\n   * }\n   * @param {Function} endpoint - Function that returns a promise for the next page of results\n   * @yields {object[]} Each page of results\n   */\n  async *paginate(endpoint) {\n    const response = await endpoint();\n    yield response.results;\n    if (response.next) {\n      const nextPage = () =>\n        this.request(response.next, { method: \"GET\" }).then((r) => r.json());\n      yield* this.paginate(nextPage);\n    }\n  }\n\n  /**\n   * Wait for a prediction to finish.\n   *\n   * If the prediction has already finished,\n   * this function returns immediately.\n   * Otherwise, it polls the API until the prediction finishes.\n   *\n   * @async\n   * @param {object} prediction - Prediction object\n   * @param {object} options - Options\n   * @param {number} [options.interval] - Polling interval in milliseconds. Defaults to 500\n   * @param {Function} [stop] - Async callback function that is called after each polling attempt. Receives the prediction object as an argument. Return false to cancel polling.\n   * @throws {Error} If the prediction doesn't complete within the maximum number of attempts\n   * @throws {Error} If the prediction failed\n   * @returns {Promise<object>} Resolves with the completed prediction object\n   */\n  async wait(prediction, options, stop) {\n    const { id } = prediction;\n    if (!id) {\n      throw new Error(\"Invalid prediction\");\n    }\n\n    if (\n      prediction.status === \"succeeded\" ||\n      prediction.status === \"failed\" ||\n      prediction.status === \"canceled\"\n    ) {\n      return prediction;\n    }\n\n    // eslint-disable-next-line no-promise-executor-return\n    const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\n\n    const interval = (options && options.interval) || 500;\n\n    let updatedPrediction = await this.predictions.get(id);\n\n    while (\n      updatedPrediction.status !== \"succeeded\" &&\n      updatedPrediction.status !== \"failed\" &&\n      updatedPrediction.status !== \"canceled\"\n    ) {\n      /* eslint-disable no-await-in-loop */\n      if (stop && (await stop(updatedPrediction)) === true) {\n        break;\n      }\n\n      await sleep(interval);\n      updatedPrediction = await this.predictions.get(prediction.id);\n      /* eslint-enable no-await-in-loop */\n    }\n\n    if (updatedPrediction.status === \"failed\") {\n      throw new Error(`Prediction failed: ${updatedPrediction.error}`);\n    }\n\n    return updatedPrediction;\n  }\n}\n\nmodule.exports = Replicate;\nmodule.exports.validateWebhook = validateWebhook;\nmodule.exports.parseProgressFromLogs = parseProgressFromLogs;\n"], "mappings": ";;;;;;AAAA;AAAA;AAGA,QAAM,WAAN,cAAuB,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAS3B,YAAY,SAAS,SAAS,UAAU;AACtC,cAAM,OAAO;AACb,aAAK,OAAO;AACZ,aAAK,UAAU;AACf,aAAK,WAAW;AAAA,MAClB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACpBjB;AAAA;AAGA,QAAM,yBAAN,MAAM,wBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM3B,YAAY,OAAO,MAAM,UAAU,MAAM;AACvC,aAAK,QAAQ;AACb,aAAK,OAAO;AACZ,aAAK,UAAU;AAAA,MACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,OAAO,MAAM,KAAK;AAChB,cAAM,QAAQ,IAAI;AAAA,UAChB;AAAA,QACF;AACA,YAAI,CAAC,OAAO;AACV,gBAAM,IAAI;AAAA,YACR,uCAAuC,GAAG;AAAA,UAC5C;AAAA,QACF;AAEA,cAAM,EAAE,OAAO,MAAM,QAAQ,IAAI,MAAM;AAEvC,eAAO,IAAI,wBAAuB,OAAO,MAAM,OAAO;AAAA,MACxD;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtCjB;AAAA;AAOA,mBAAe,WAAW,MAAM,WAAW,CAAC,GAAG;AAC7C,YAAM,OAAO,IAAI,SAAS;AAE1B,UAAI;AACJ,UAAI;AACJ,UAAI,gBAAgB,MAAM;AACxB,mBAAW,KAAK,QAAQ,QAAQ,KAAK,IAAI,CAAC;AAC1C,eAAO;AAAA,MACT,WAAW,OAAO,SAAS,IAAI,GAAG;AAChC,mBAAW,UAAU,KAAK,IAAI,CAAC;AAC/B,cAAM,QAAQ,IAAI,WAAW,IAAI;AACjC,eAAO,IAAI,KAAK,CAAC,KAAK,GAAG;AAAA,UACvB,MAAM;AAAA,UACN,MAAM;AAAA,QACR,CAAC;AAAA,MACH,OAAO;AACL,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AAEA,WAAK,OAAO,WAAW,MAAM,QAAQ;AACrC,WAAK;AAAA,QACH;AAAA,QACA,IAAI,KAAK,CAAC,KAAK,UAAU,QAAQ,CAAC,GAAG,EAAE,MAAM,mBAAmB,CAAC;AAAA,MACnE;AAEA,YAAM,WAAW,MAAM,KAAK,QAAQ,UAAU;AAAA,QAC5C,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAOA,mBAAe,YAAY;AACzB,YAAM,WAAW,MAAM,KAAK,QAAQ,UAAU;AAAA,QAC5C,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAQA,mBAAe,QAAQ,SAAS;AAC9B,YAAM,WAAW,MAAM,KAAK,QAAQ,UAAU,OAAO,IAAI;AAAA,QACvD,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAQA,mBAAe,WAAW,SAAS;AACjC,YAAM,WAAW,MAAM,KAAK,QAAQ,UAAU,OAAO,IAAI;AAAA,QACvD,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,WAAW;AAAA,IAC7B;AAEA,WAAO,UAAU;AAAA,MACf,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,KAAK;AAAA,MACL,QAAQ;AAAA,IACV;AAAA;AAAA;;;ACzFA;AAAA;AAAA,QAAM,WAAW;AACjB,QAAM,EAAE,QAAQ,WAAW,IAAI;AA+B/B,mBAAe,gBAAgB,aAAa,QAAQ;AAClD,UAAI,EAAE,IAAI,WAAW,MAAM,UAAU,IAAI;AACzC,YAAM,gBAAgB,UAAU,YAAY;AAE5C,UAAI,eAAe,YAAY,WAAW,YAAY,MAAM;AAC1D,YAAI,OAAO,YAAY,QAAQ,QAAQ,YAAY;AAEjD,eAAK,YAAY,QAAQ,IAAI,YAAY;AACzC,sBAAY,YAAY,QAAQ,IAAI,mBAAmB;AACvD,sBAAY,YAAY,QAAQ,IAAI,mBAAmB;AAAA,QACzD,OAAO;AAEL,eAAK,YAAY,QAAQ,YAAY;AACrC,sBAAY,YAAY,QAAQ,mBAAmB;AACnD,sBAAY,YAAY,QAAQ,mBAAmB;AAAA,QACrD;AACA,eAAO,YAAY;AAAA,MACrB;AAEA,UAAI,gBAAgB,kBAAkB,KAAK,UAAU;AACnD,YAAI;AACF,iBAAO,MAAM,IAAI,SAAS,IAAI,EAAE,KAAK;AAAA,QACvC,SAAS,KAAK;AACZ,gBAAM,IAAI,MAAM,uBAAuB,IAAI,OAAO,EAAE;AAAA,QACtD;AAAA,MACF,WAAW,aAAa,IAAI,GAAG;AAC7B,eAAO,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK;AAAA,MACrC,WAAW,OAAO,SAAS,UAAU;AACnC,eAAO,KAAK,UAAU,IAAI;AAAA,MAC5B,WAAW,OAAO,SAAS,UAAU;AACnC,cAAM,IAAI,MAAM,mBAAmB;AAAA,MACrC;AAEA,UAAI,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW;AACnC,cAAM,IAAI,MAAM,kCAAkC;AAAA,MACpD;AAEA,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACzC;AAEA,UAAI,CAAC,eAAe;AAClB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC3C;AAEA,YAAM,gBAAgB,GAAG,EAAE,IAAI,SAAS,IAAI,IAAI;AAEhD,YAAM,oBAAoB,MAAM;AAAA,QAC9B,cAAc,MAAM,GAAG,EAAE,IAAI;AAAA,QAC7B;AAAA,MACF;AAEA,YAAM,qBAAqB,UACxB,MAAM,GAAG,EACT,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC;AAEjC,aAAO,mBAAmB;AAAA,QACxB,CAAC,sBAAsB,sBAAsB;AAAA,MAC/C;AAAA,IACF;AAMA,mBAAe,iBAAiB,QAAQ,MAAM;AAC5C,YAAM,UAAU,IAAI,YAAY;AAChC,UAAI,SAAS,WAAW;AAGxB,UAAI,OAAO,WAAW,eAAe,OAAO,cAAY,YAAY;AAYlE,iBAAS,UAAQ,KAAK,MAAM,aAAa,EAAE;AAAA,MAC7C;AAEA,YAAM,MAAM,MAAM,OAAO,OAAO;AAAA,QAC9B;AAAA,QACA,cAAc,MAAM;AAAA,QACpB,EAAE,MAAM,QAAQ,MAAM,UAAU;AAAA,QAChC;AAAA,QACA,CAAC,MAAM;AAAA,MACT;AAEA,YAAM,YAAY,MAAM,OAAO,OAAO,KAAK,QAAQ,KAAK,QAAQ,OAAO,IAAI,CAAC;AAC5E,aAAO,cAAc,SAAS;AAAA,IAChC;AAiBA,aAAS,cAAc,QAAQ;AAC7B,aAAO,WAAW,KAAK,KAAK,MAAM,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAAA,IAC9D;AAUA,aAAS,cAAc,OAAO;AAC5B,aAAO,KAAK,OAAO,aAAa,MAAM,MAAM,IAAI,WAAW,KAAK,CAAC,CAAC;AAAA,IACpE;AAqBA,mBAAe,qBAAqB,SAAS,UAAU,CAAC,GAAG;AACzD,YAAM,cAAc,QAAQ,gBAAgB,MAAM;AAClD,YAAM,aAAa,QAAQ,cAAc;AACzC,YAAM,WAAW,QAAQ,YAAY;AACrC,YAAM,SAAS,QAAQ,UAAU;AAGjC,YAAM,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAEtE,UAAI,WAAW;AACf,SAAG;AACD,YAAI,QAAQ,WAAW,KAAK,WAAW,KAAK,OAAO,IAAI;AAGvD,YAAI;AACF,gBAAM,WAAW,MAAM,QAAQ;AAC/B,cAAI,SAAS,MAAM,CAAC,YAAY,QAAQ,GAAG;AACzC,mBAAO;AAAA,UACT;AAAA,QACF,SAAS,OAAO;AACd,cAAI,iBAAiB,UAAU;AAC7B,kBAAM,aAAa,MAAM,SAAS,QAAQ,IAAI,aAAa;AAC3D,gBAAI,YAAY;AACd,kBAAI,CAAC,OAAO,UAAU,UAAU,GAAG;AAEjC,sBAAM,OAAO,IAAI,KAAK,UAAU;AAChC,oBAAI,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC,GAAG;AACjC,0BAAQ,KAAK,QAAQ,KAAI,oBAAI,KAAK,GAAE,QAAQ;AAAA,gBAC9C;AAAA,cACF,OAAO;AAEL,wBAAQ,aAAa;AAAA,cACvB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,YAAI,OAAO,UAAU,UAAU,KAAK,aAAa,GAAG;AAClD,cAAI,OAAO,UAAU,KAAK,KAAK,QAAQ,GAAG;AACxC,kBAAM,MAAM,WAAW,MAAM,QAAQ,aAAa,WAAW;AAAA,UAC/D;AACA,sBAAY;AAAA,QACd;AAAA,MACF,SAAS,WAAW;AAEpB,aAAO,QAAQ;AAAA,IACjB;AAYA,mBAAe,oBAAoB,QAAQ,QAAQ,UAAU;AAC3D,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,iBAAO,MAAM,2CAA2C,QAAQ,MAAM;AAAA,QACxE,KAAK;AACH,iBAAO,MAAM,uCAAuC,QAAQ,MAAM;AAAA,QACpE,KAAK;AACH,cAAI;AACF,mBAAO,MAAM,uCAAuC,QAAQ,MAAM;AAAA,UACpE,SAAS,OAAO;AACd,gBACE,iBAAiB,YACjB,MAAM,SAAS,UAAU,OACzB,MAAM,SAAS,SAAS,KACxB;AACA,oBAAM;AAAA,YACR;AACA,mBAAO,MAAM,2CAA2C,MAAM;AAAA,UAChE;AAAA,QACF;AACE,gBAAM,IAAI,MAAM,oCAAoC,QAAQ,EAAE;AAAA,MAClE;AAAA,IACF;AAWA,mBAAe,uCAAuC,QAAQ,QAAQ;AACpE,aAAO,MAAM,UAAU,QAAQ,OAAO,UAAU;AAC9C,YAAI,iBAAiB,QAAQ,iBAAiB,QAAQ;AACpD,gBAAM,OAAO,MAAM,WAAW,KAAK,QAAQ,KAAK;AAChD,iBAAO,KAAK,KAAK;AAAA,QACnB;AAEA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,QAAM,oBAAoB;AAU1B,mBAAe,2CAA2C,QAAQ;AAChE,UAAI,aAAa;AACjB,aAAO,MAAM,UAAU,QAAQ,OAAO,UAAU;AAC9C,YAAI;AACJ,YAAI;AAEJ,YAAI,iBAAiB,MAAM;AAOzB,mBAAS,MAAM,MAAM,YAAY;AACjC,iBAAO,MAAM;AAAA,QACf,WAAW,aAAa,KAAK,GAAG;AAC9B,mBAAS;AAAA,QACX,OAAO;AACL,iBAAO;AAAA,QACT;AAEA,sBAAc,OAAO;AACrB,YAAI,aAAa,mBAAmB;AAClC,gBAAM,IAAI;AAAA,YACR,mCAAmC,UAAU;AAAA,UAC/C;AAAA,QACF;AAEA,cAAM,OAAO,cAAc,MAAM;AACjC,eAAO,QAAQ;AAEf,eAAO,QAAQ,IAAI,WAAW,IAAI;AAAA,MACpC,CAAC;AAAA,IACH;AAGA,mBAAe,UAAU,OAAO,QAAQ;AACtC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,cAAM,OAAO,CAAC;AACd,mBAAW,OAAO,OAAO;AACvB,gBAAM,cAAc,MAAM,UAAU,KAAK,MAAM;AAC/C,eAAK,KAAK,WAAW;AAAA,QACvB;AACA,eAAO;AAAA,MACT;AAEA,UAAI,cAAc,KAAK,GAAG;AACxB,cAAM,OAAO,CAAC;AACd,mBAAW,OAAO,OAAO,KAAK,KAAK,GAAG;AACpC,eAAK,GAAG,IAAI,MAAM,UAAU,MAAM,GAAG,GAAG,MAAM;AAAA,QAChD;AACA,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,OAAO,KAAK;AAAA,IAC3B;AAEA,aAAS,aAAa,KAAK;AACzB,aACE,eAAe,aACf,eAAe,cACf,eAAe,cACf,eAAe,cACf,eAAe,qBACf,eAAe,eACf,eAAe,eACf,eAAe,gBACf,eAAe;AAAA,IAEnB;AAIA,aAAS,cAAc,OAAO;AAC5B,YAAM,eAAe,OAAO,UAAU,YAAY,UAAU;AAC5D,UAAI,CAAC,gBAAgB,OAAO,KAAK,MAAM,mBAAmB;AACxD,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,OAAO,eAAe,KAAK;AACzC,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AACA,YAAM,OACJ,OAAO,UAAU,eAAe,KAAK,OAAO,aAAa,KACzD,MAAM;AACR,aACE,OAAO,SAAS,cAChB,gBAAgB,QAChB,SAAS,UAAU,SAAS,KAAK,IAAI,MACnC,SAAS,UAAU,SAAS,KAAK,MAAM;AAAA,IAE7C;AAwBA,aAAS,sBAAsB,OAAO;AACpC,YAAM,OAAO,OAAO,UAAU,YAAY,MAAM,OAAO,MAAM,OAAO;AACpE,UAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;AACrC,eAAO;AAAA,MACT;AAEA,YAAM,UAAU;AAChB,YAAM,QAAQ,KAAK,MAAM,IAAI,EAAE,QAAQ;AAEvC,iBAAW,QAAQ,OAAO;AACxB,cAAM,UAAU,KAAK,MAAM,OAAO;AAElC,YAAI,WAAW,QAAQ,WAAW,GAAG;AACnC,iBAAO;AAAA,YACL,YAAY,SAAS,QAAQ,CAAC,GAAG,EAAE,IAAI;AAAA,YACvC,SAAS,SAAS,QAAQ,CAAC,GAAG,EAAE;AAAA,YAChC,OAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAAA,UAChC;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAYA,oBAAgB,oBAAoB,QAAQ;AAC1C,YAAM,SAAS,OAAO,UAAU;AAChC,UAAI;AACF,eAAO,MAAM;AACX,gBAAM,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,KAAK;AAC1C,cAAI,KAAM;AACV,gBAAM;AAAA,QACR;AAAA,MACF,UAAE;AACA,eAAO,YAAY;AAAA,MACrB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC5cA;AAAA;AAuBA,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAK,QAAQ,OAAO,SAAS,YAAa,OAAO,SAAS,YAAY;AACpE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK;AAAA,cACjB,KAAK,MAAM,KAAK,GAAG;AAAA,cACnB,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK;AAAA,YAC5D,CAAC;AAAA,MACP;AACA,aAAO;AAAA,IACT;AACA,QAAI,eAAe,CAAC,QAClB,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AAG/D,QAAI,gBAAgB,CAAC;AACrB,aAAS,eAAe;AAAA,MACtB,yBAAyB,MAAM;AAAA,IACjC,CAAC;AACD,WAAO,UAAU,aAAa,aAAa;AAG3C,aAAS,aAAa,SAAS;AAC7B,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,YAAM;AACN,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AACA,eAAS,QAAQ;AACf,uBAAe;AACf,iBAAS;AACT,2BAAmB;AACnB,8BAAsB;AACtB,kBAAU;AACV,oBAAY;AACZ,eAAO;AAAA,MACT;AACA,eAAS,KAAK,OAAO;AACnB,iBAAS,SAAS,SAAS,QAAQ;AACnC,YAAI,gBAAgB,OAAO,MAAM,GAAG;AAClC,mBAAS,OAAO,MAAM,IAAI,MAAM;AAAA,QAClC;AACA,uBAAe;AACf,cAAM,SAAS,OAAO;AACtB,YAAI,WAAW;AACf,YAAI,yBAAyB;AAC7B,eAAO,WAAW,QAAQ;AACxB,cAAI,wBAAwB;AAC1B,gBAAI,OAAO,QAAQ,MAAM,MAAM;AAC7B,gBAAE;AAAA,YACJ;AACA,qCAAyB;AAAA,UAC3B;AACA,cAAI,aAAa;AACjB,cAAI,cAAc;AAClB,cAAI;AACJ,mBACM,QAAQ,kBACZ,aAAa,KAAK,QAAQ,QAC1B,EAAE,OACF;AACA,wBAAY,OAAO,KAAK;AACxB,gBAAI,cAAc,OAAO,cAAc,GAAG;AACxC,4BAAc,QAAQ;AAAA,YACxB,WAAW,cAAc,MAAM;AAC7B,uCAAyB;AACzB,2BAAa,QAAQ;AAAA,YACvB,WAAW,cAAc,MAAM;AAC7B,2BAAa,QAAQ;AAAA,YACvB;AAAA,UACF;AACA,cAAI,aAAa,GAAG;AAClB,+BAAmB,SAAS;AAC5B,kCAAsB;AACtB;AAAA,UACF,OAAO;AACL,+BAAmB;AACnB,kCAAsB;AAAA,UACxB;AACA,+BAAqB,QAAQ,UAAU,aAAa,UAAU;AAC9D,sBAAY,aAAa;AAAA,QAC3B;AACA,YAAI,aAAa,QAAQ;AACvB,mBAAS;AAAA,QACX,WAAW,WAAW,GAAG;AACvB,mBAAS,OAAO,MAAM,QAAQ;AAAA,QAChC;AAAA,MACF;AACA,eAAS,qBAAqB,YAAY,OAAO,aAAa,YAAY;AACxE,YAAI,eAAe,GAAG;AACpB,cAAI,KAAK,SAAS,GAAG;AACnB,oBAAQ;AAAA,cACN,MAAM;AAAA,cACN,IAAI;AAAA,cACJ,OAAO,aAAa;AAAA,cACpB,MAAM,KAAK,MAAM,GAAG,EAAE;AAAA;AAAA,YAExB,CAAC;AACD,mBAAO;AACP,sBAAU;AAAA,UACZ;AACA,sBAAY;AACZ;AAAA,QACF;AACA,cAAM,UAAU,cAAc;AAC9B,cAAM,QAAQ,WAAW;AAAA,UACvB;AAAA,UACA,SAAS,UAAU,aAAa;AAAA,QAClC;AACA,YAAI,OAAO;AACX,YAAI,SAAS;AACX,iBAAO;AAAA,QACT,WAAW,WAAW,QAAQ,cAAc,CAAC,MAAM,KAAK;AACtD,iBAAO,cAAc;AAAA,QACvB,OAAO;AACL,iBAAO,cAAc;AAAA,QACvB;AACA,cAAM,WAAW,QAAQ;AACzB,cAAM,cAAc,aAAa;AACjC,cAAM,QAAQ,WAAW,MAAM,UAAU,WAAW,WAAW,EAAE,SAAS;AAC1E,YAAI,UAAU,QAAQ;AACpB,kBAAQ,QAAQ,GAAG,OAAO,OAAO,IAAI,IAAI;AAAA,QAC3C,WAAW,UAAU,SAAS;AAC5B,sBAAY;AAAA,QACd,WAAW,UAAU,QAAQ,CAAC,MAAM,SAAS,IAAI,GAAG;AAClD,oBAAU;AAAA,QACZ,WAAW,UAAU,SAAS;AAC5B,gBAAM,QAAQ,SAAS,OAAO,EAAE;AAChC,cAAI,CAAC,OAAO,MAAM,KAAK,GAAG;AACxB,oBAAQ;AAAA,cACN,MAAM;AAAA,cACN,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,CAAC,KAAK,KAAK,GAAG;AACxB,aAAS,OAAO,QAAQ;AACtB,aAAO,IAAI,MAAM,CAAC,UAAU,UAAU,OAAO,WAAW,KAAK,MAAM,QAAQ;AAAA,IAC7E;AAGA,QAAI,0BAA0B,cAAc,gBAAgB;AAAA,MAC1D,cAAc;AACZ,YAAI;AACJ,cAAM;AAAA,UACJ,MAAM,YAAY;AAChB,qBAAS,aAAa,CAAC,UAAU;AAC/B,kBAAI,MAAM,SAAS,SAAS;AAC1B,2BAAW,QAAQ,KAAK;AAAA,cAC1B;AAAA,YACF,CAAC;AAAA,UACH;AAAA,UACA,UAAU,OAAO;AACf,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;;;ACrMA;AAAA;AAuBA,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AAGzF,QAAI,gBAAgB,CAAC;AACrB,aAAS,eAAe;AAAA,MACtB,mBAAmB,MAAM;AAAA,IAC3B,CAAC;AACD,WAAO,UAAU,aAAa,aAAa;AAG3C,QAAI,aAAa,OAAO,YAAY;AACpC,QAAI,eAAe,OAAO,cAAc;AACxC,QAAI,wBAAwB,MAAM;AAAA,MAChC,YAAY,SAAS;AACnB,aAAK,WAAW;AAAA,MAClB;AAAA,MACA,UAAU,OAAO,YAAY;AAC3B,YAAI,EAAE,iBAAiB,eAAe,YAAY,OAAO,KAAK,IAAI;AAChE,gBAAM,IAAI,UAAU,mCAAmC;AAAA,QACzD;AACA,cAAM,OAAO,KAAK,SAAS,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC;AACzD,YAAI,KAAK,WAAW,GAAG;AACrB,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,MACA,MAAM,YAAY;AAChB,cAAM,OAAO,KAAK,SAAS,OAAO;AAClC,YAAI,KAAK,WAAW,GAAG;AACrB,qBAAW,QAAQ,IAAI;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,oBAAoB,MAAM;AAAA,MAC5B,YAAY,OAAO,SAAS;AAC1B,cAAM,UAAU,IAAI,YAAY,SAAS,SAAS,WAAW,CAAC,CAAC;AAC/D,aAAK,UAAU,IAAI;AACnB,aAAK,YAAY,IAAI,IAAI,gBAAgB,IAAI,sBAAsB,OAAO,CAAC;AAAA,MAC7E;AAAA,MACA,IAAI,WAAW;AACb,eAAO,KAAK,UAAU,EAAE;AAAA,MAC1B;AAAA,MACA,IAAI,QAAQ;AACV,eAAO,KAAK,UAAU,EAAE;AAAA,MAC1B;AAAA,MACA,IAAI,YAAY;AACd,eAAO,KAAK,UAAU,EAAE;AAAA,MAC1B;AAAA,MACA,IAAI,WAAW;AACb,eAAO,KAAK,YAAY,EAAE;AAAA,MAC5B;AAAA,MACA,IAAI,WAAW;AACb,eAAO,KAAK,YAAY,EAAE;AAAA,MAC5B;AAAA,IACF;AACA,QAAI,aAAa,OAAO,YAAY;AACpC,QAAI,eAAe,OAAO,cAAc;AAAA;AAAA;;;AC9FxC,IAAAA,kBAAA;AAAA;AAEA,QAAM,WAAW;AACjB,QAAM,EAAE,oBAAoB,IAAI;AAChC,QAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAM,EAAE,kBAAkB,IACxB,OAAO,WAAW,sBAAsB,cACpC,gCACA;AAKN,QAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASpB,YAAY,OAAO,MAAM,IAAI,OAAO;AAClC,aAAK,QAAQ;AACb,aAAK,OAAO;AACZ,aAAK,KAAK;AACV,aAAK,QAAQ;AAAA,MACf;AAAA;AAAA;AAAA;AAAA,MAKA,WAAW;AACT,YAAI,KAAK,UAAU,UAAU;AAC3B,iBAAO,KAAK;AAAA,QACd;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAYA,aAAS,qBAAqB,EAAE,KAAK,OAAO,UAAU,CAAC,EAAE,GAAG;AAC1D,YAAM,EAAE,gBAAgB,MAAM,UAAU,CAAC,GAAG,GAAG,YAAY,IAAI;AAE/D,aAAO,IAAI,eAAe;AAAA,QACxB,MAAM,MAAM,YAAY;AACtB,gBAAMC,QAAO;AAAA,YACX,GAAG;AAAA,YACH,SAAS;AAAA,cACP,GAAG;AAAA,cACH,QAAQ;AAAA,YACV;AAAA,UACF;AACA,gBAAM,WAAW,MAAM,MAAM,KAAKA,KAAI;AAEtC,cAAI,CAAC,SAAS,IAAI;AAChB,kBAAM,OAAO,MAAM,SAAS,KAAK;AACjC,kBAAM,UAAU,IAAI,QAAQ,KAAKA,KAAI;AACrC,uBAAW;AAAA,cACT,IAAI;AAAA,gBACF,cAAc,GAAG,uBAAuB,SAAS,MAAM,KAAK,IAAI;AAAA,gBAChE;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,gBAAM,SAAS,SAAS,KACrB,YAAY,IAAI,kBAAkB,CAAC,EACnC,YAAY,IAAI,wBAAwB,CAAC;AAE5C,2BAAiB,SAAS,oBAAoB,MAAM,GAAG;AACrD,gBAAI,MAAM,UAAU,SAAS;AAC3B,yBAAW,MAAM,IAAI,MAAM,MAAM,IAAI,CAAC;AACtC;AAAA,YACF;AAEA,gBAAI,OAAO,MAAM;AACjB,gBACE,iBACA,OAAO,SAAS,aACf,KAAK,WAAW,QAAQ,KAAK,KAAK,WAAW,OAAO,IACrD;AACA,qBAAO,iBAAiB,EAAE,MAAM,MAAM,CAAC;AAAA,YACzC;AACA,uBAAW,QAAQ,IAAI,gBAAgB,MAAM,OAAO,MAAM,MAAM,EAAE,CAAC;AAEnE,gBAAI,MAAM,UAAU,QAAQ;AAC1B;AAAA,YACF;AAAA,UACF;AAEA,qBAAW,MAAM;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAWA,aAAS,iBAAiB,EAAE,KAAK,MAAM,GAAG;AACxC,UAAI,OAAO;AAAA,MAEX,MAAM,mBAAmB,eAAe;AAAA,QACtC,MAAM,OAAO;AACX,gBAAM,SAAS,CAAC;AAChB,2BAAiB,SAAS,MAAM;AAC9B,mBAAO,KAAK,KAAK;AAAA,UACnB;AACA,iBAAO,IAAI,KAAK,QAAQ,EAAE,KAAK,CAAC;AAAA,QAClC;AAAA,QAEA,MAAM;AACJ,iBAAO,IAAI,IAAI,GAAG;AAAA,QACpB;AAAA,QAEA,WAAW;AACT,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,aAAO,IAAI,WAAW;AAAA,QACpB,MAAM,MAAM,YAAY;AACtB,gBAAM,WAAW,MAAM,MAAM,GAAG;AAEhC,cAAI,CAAC,SAAS,IAAI;AAChB,kBAAM,OAAO,MAAM,SAAS,KAAK;AACjC,kBAAM,UAAU,IAAI,QAAQ,KAAK,IAAI;AACrC,uBAAW;AAAA,cACT,IAAI;AAAA,gBACF,cAAc,GAAG,uBAAuB,SAAS,MAAM,KAAK,IAAI;AAAA,gBAChE;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,cAAI,SAAS,QAAQ,IAAI,cAAc,GAAG;AACxC,mBAAO,SAAS,QAAQ,IAAI,cAAc;AAAA,UAC5C;AAEA,cAAI;AACF,6BAAiB,SAAS,oBAAoB,SAAS,IAAI,GAAG;AAC5D,yBAAW,QAAQ,KAAK;AAAA,YAC1B;AACA,uBAAW,MAAM;AAAA,UACnB,SAAS,KAAK;AACZ,uBAAW,MAAM,GAAG;AAAA,UACtB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC/KA;AAAA;AAKA,mBAAe,oBAAoB;AACjC,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAAA,QAC9C,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA,MACf,SAAS;AAAA,IACX;AAAA;AAAA;;;ACfA;AAAA;AAMA,mBAAe,cAAc,iBAAiB;AAC5C,YAAM,WAAW,MAAM,KAAK,QAAQ,gBAAgB,eAAe,IAAI;AAAA,QACrE,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAOA,mBAAe,kBAAkB;AAC/B,YAAM,WAAW,MAAM,KAAK,QAAQ,gBAAgB;AAAA,QAClD,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,WAAO,UAAU,EAAE,KAAK,eAAe,MAAM,gBAAgB;AAAA;AAAA;;;AC3B7D;AAAA;AAAA,QAAM,EAAE,oBAAoB,IAAI;AAchC,mBAAe,iBAAiB,kBAAkB,iBAAiB,SAAS;AAC1E,YAAM,EAAE,OAAO,MAAM,GAAG,KAAK,IAAI;AAEjC,UAAI,KAAK,SAAS;AAChB,YAAI;AAEF,cAAI,IAAI,KAAK,OAAO;AAAA,QACtB,SAAS,KAAK;AACZ,gBAAM,IAAI,MAAM,qBAAqB;AAAA,QACvC;AAAA,MACF;AAEA,YAAM,UAAU,CAAC;AACjB,UAAI,MAAM;AACR,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,IAAI,KAAK,IAAI,GAAG,KAAK,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC;AAClD,kBAAQ,QAAQ,IAAI,QAAQ,CAAC;AAAA,QAC/B,OAAO;AACL,kBAAQ,QAAQ,IAAI;AAAA,QACtB;AAAA,MACF;AAEA,YAAM,WAAW,MAAM,KAAK;AAAA,QAC1B,gBAAgB,gBAAgB,IAAI,eAAe;AAAA,QACnD;AAAA,UACE,QAAQ;AAAA,UACR;AAAA,UACA,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,OAAO,MAAM;AAAA,cACX;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO,SAAS,KAAK;AAAA,IACvB;AASA,mBAAe,cAAc,kBAAkB,iBAAiB;AAC9D,YAAM,WAAW,MAAM,KAAK;AAAA,QAC1B,gBAAgB,gBAAgB,IAAI,eAAe;AAAA,QACnD;AAAA,UACE,QAAQ;AAAA,QACV;AAAA,MACF;AAEA,aAAO,SAAS,KAAK;AAAA,IACvB;AAkBA,mBAAe,iBAAiB,mBAAmB;AACjD,YAAM,WAAW,MAAM,KAAK,QAAQ,gBAAgB;AAAA,QAClD,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAkBA,mBAAe,iBACb,kBACA,iBACA,mBACA;AACA,YAAM,WAAW,MAAM,KAAK;AAAA,QAC1B,gBAAgB,gBAAgB,IAAI,eAAe;AAAA,QACnD;AAAA,UACE,QAAQ;AAAA,UACR,MAAM;AAAA,QACR;AAAA,MACF;AAEA,aAAO,SAAS,KAAK;AAAA,IACvB;AASA,mBAAe,iBAAiB,kBAAkB,iBAAiB;AACjE,YAAM,WAAW,MAAM,KAAK;AAAA,QAC1B,gBAAgB,gBAAgB,IAAI,eAAe;AAAA,QACnD;AAAA,UACE,QAAQ;AAAA,QACV;AAAA,MACF;AAEA,aAAO,SAAS,WAAW;AAAA,IAC7B;AAOA,mBAAe,kBAAkB;AAC/B,YAAM,WAAW,MAAM,KAAK,QAAQ,gBAAgB;AAAA,QAClD,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA,MACf,aAAa;AAAA,QACX,QAAQ;AAAA,MACV;AAAA,MACA,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,IACV;AAAA;AAAA;;;AC1KA;AAAA;AAKA,mBAAe,eAAe;AAC5B,YAAM,WAAW,MAAM,KAAK,QAAQ,aAAa;AAAA,QAC/C,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA,MACf,MAAM;AAAA,IACR;AAAA;AAAA;;;ACfA;AAAA;AAOA,mBAAe,SAAS,aAAa,YAAY;AAC/C,YAAM,WAAW,MAAM,KAAK,QAAQ,WAAW,WAAW,IAAI,UAAU,IAAI;AAAA,QAC1E,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AASA,mBAAe,kBAAkB,aAAa,YAAY;AACxD,YAAM,WAAW,MAAM,KAAK;AAAA,QAC1B,WAAW,WAAW,IAAI,UAAU;AAAA,QACpC;AAAA,UACE,QAAQ;AAAA,QACV;AAAA,MACF;AAEA,aAAO,SAAS,KAAK;AAAA,IACvB;AAUA,mBAAe,gBAAgB,aAAa,YAAY,YAAY;AAClE,YAAM,WAAW,MAAM,KAAK;AAAA,QAC1B,WAAW,WAAW,IAAI,UAAU,aAAa,UAAU;AAAA,QAC3D;AAAA,UACE,QAAQ;AAAA,QACV;AAAA,MACF;AAEA,aAAO,SAAS,KAAK;AAAA,IACvB;AAOA,mBAAe,aAAa;AAC1B,YAAM,WAAW,MAAM,KAAK,QAAQ,WAAW;AAAA,QAC7C,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAiBA,mBAAe,YAAY,aAAa,YAAY,SAAS;AAC3D,YAAM,OAAO,EAAE,OAAO,aAAa,MAAM,YAAY,GAAG,QAAQ;AAEhE,YAAM,WAAW,MAAM,KAAK,QAAQ,WAAW;AAAA,QAC7C,QAAQ;AAAA,QACR;AAAA,MACF,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAQA,mBAAe,OAAO,OAAO;AAC3B,YAAM,WAAW,MAAM,KAAK,QAAQ,WAAW;AAAA,QAC7C,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM;AAAA,MACR,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA,MACf,KAAK;AAAA,MACL,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,UAAU,EAAE,MAAM,mBAAmB,KAAK,gBAAgB;AAAA,MAC1D;AAAA,IACF;AAAA;AAAA;;;ACnHA;AAAA;AAAA,QAAM,EAAE,oBAAoB,IAAI;AAchC,mBAAe,iBAAiB,SAAS;AACvC,YAAM,EAAE,OAAO,SAAS,OAAO,MAAM,GAAG,KAAK,IAAI;AAEjD,UAAI,KAAK,SAAS;AAChB,YAAI;AAEF,cAAI,IAAI,KAAK,OAAO;AAAA,QACtB,SAAS,KAAK;AACZ,gBAAM,IAAI,MAAM,qBAAqB;AAAA,QACvC;AAAA,MACF;AAEA,YAAM,UAAU,CAAC;AACjB,UAAI,MAAM;AACR,YAAI,OAAO,SAAS,UAAU;AAC5B,gBAAM,IAAI,KAAK,IAAI,GAAG,KAAK,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC;AAClD,kBAAQ,QAAQ,IAAI,QAAQ,CAAC;AAAA,QAC/B,OAAO;AACL,kBAAQ,QAAQ,IAAI;AAAA,QACtB;AAAA,MACF;AAEA,UAAI;AACJ,UAAI,SAAS;AACX,mBAAW,MAAM,KAAK,QAAQ,gBAAgB;AAAA,UAC5C,QAAQ;AAAA,UACR;AAAA,UACA,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,OAAO,MAAM;AAAA,cACX;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,WAAW,OAAO;AAChB,mBAAW,MAAM,KAAK,QAAQ,WAAW,KAAK,gBAAgB;AAAA,UAC5D,QAAQ;AAAA,UACR;AAAA,UACA,MAAM;AAAA,YACJ,GAAG;AAAA,YACH,OAAO,MAAM;AAAA,cACX;AAAA,cACA;AAAA,cACA,KAAK;AAAA,YACP;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,cAAM,IAAI,MAAM,2CAA2C;AAAA,MAC7D;AAEA,aAAO,SAAS,KAAK;AAAA,IACvB;AAQA,mBAAe,cAAc,eAAe;AAC1C,YAAM,WAAW,MAAM,KAAK,QAAQ,gBAAgB,aAAa,IAAI;AAAA,QACnE,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAQA,mBAAe,iBAAiB,eAAe;AAC7C,YAAM,WAAW,MAAM,KAAK,QAAQ,gBAAgB,aAAa,WAAW;AAAA,QAC1E,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAOA,mBAAe,kBAAkB;AAC/B,YAAM,WAAW,MAAM,KAAK,QAAQ,gBAAgB;AAAA,QAClD,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA,MACf,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA;AAAA;;;ACrHA;AAAA;AAaA,mBAAe,eAAe,aAAa,YAAY,YAAY,SAAS;AAC1E,YAAM,EAAE,GAAG,KAAK,IAAI;AAEpB,UAAI,KAAK,SAAS;AAChB,YAAI;AAEF,cAAI,IAAI,KAAK,OAAO;AAAA,QACtB,SAAS,KAAK;AACZ,gBAAM,IAAI,MAAM,qBAAqB;AAAA,QACvC;AAAA,MACF;AAEA,YAAM,WAAW,MAAM,KAAK;AAAA,QAC1B,WAAW,WAAW,IAAI,UAAU,aAAa,UAAU;AAAA,QAC3D;AAAA,UACE,QAAQ;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,aAAO,SAAS,KAAK;AAAA,IACvB;AAQA,mBAAe,YAAY,aAAa;AACtC,YAAM,WAAW,MAAM,KAAK,QAAQ,cAAc,WAAW,IAAI;AAAA,QAC/D,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAQA,mBAAe,eAAe,aAAa;AACzC,YAAM,WAAW,MAAM,KAAK,QAAQ,cAAc,WAAW,WAAW;AAAA,QACtE,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAOA,mBAAe,gBAAgB;AAC7B,YAAM,WAAW,MAAM,KAAK,QAAQ,cAAc;AAAA,QAChD,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA,MACf,QAAQ;AAAA,MACR,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA;AAAA;;;AClFA;AAAA;AAKA,mBAAe,0BAA0B;AACvC,YAAM,WAAW,MAAM,KAAK,QAAQ,4BAA4B;AAAA,QAC9D,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS,KAAK;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA,MACf,SAAS;AAAA,QACP,QAAQ;AAAA,UACN,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACnBA;AAAA;AAAA;AAAA,MACE,MAAQ;AAAA,MACR,SAAW;AAAA,MACX,aAAe;AAAA,MACf,YAAc;AAAA,MACd,UAAY;AAAA,MACZ,MAAQ;AAAA,MACR,SAAW;AAAA,MACX,MAAQ;AAAA,MACR,MAAQ;AAAA,MACR,OAAS;AAAA,MACT,OAAS;AAAA,QACP;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,MACA,SAAW;AAAA,QACT,MAAQ;AAAA,QACR,KAAO;AAAA,QACP,KAAO;AAAA,QACP,MAAQ;AAAA,MACV;AAAA,MACA,SAAW;AAAA,QACT,OAAS;AAAA,QACT,QAAU;AAAA,QACV,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,MAAQ;AAAA,QACR,MAAQ;AAAA,MACV;AAAA,MACA,sBAAwB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,iBAAmB;AAAA,QACjB,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,oCAAoC;AAAA,QACpC,eAAe;AAAA,QACf,MAAQ;AAAA,QACR,MAAQ;AAAA,QACR,SAAW;AAAA,QACX,WAAW;AAAA,QACX,YAAc;AAAA,MAChB;AAAA,IACF;AAAA;AAAA;;;ACjDA;AAAA;AAAA,QAAM,WAAW;AACjB,QAAM,yBAAyB;AAC/B,QAAM,EAAE,sBAAsB,iBAAiB,IAAI;AACnD,QAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,QAAM,WAAW;AACjB,QAAM,cAAc;AACpB,QAAM,cAAc;AACpB,QAAM,QAAQ;AACd,QAAM,WAAW;AACjB,QAAM,SAAS;AACf,QAAM,cAAc;AACpB,QAAM,YAAY;AAClB,QAAM,WAAW;AAEjB,QAAM,cAAc;AAoBpB,QAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYd,YAAY,UAAU,CAAC,GAAG;AACxB,aAAK,OACH,QAAQ,SACP,OAAO,YAAY,cAAc,QAAQ,IAAI,sBAAsB;AACtE,aAAK,YACH,QAAQ,aAAa,wBAAwB,YAAY,OAAO;AAClE,aAAK,UAAU,QAAQ,WAAW;AAClC,aAAK,QAAQ,QAAQ,SAAS,WAAW;AACzC,aAAK,uBAAuB,QAAQ,wBAAwB;AAC5D,aAAK,gBAAgB,QAAQ,kBAAkB,QAAQ,QAAQ;AAE/D,aAAK,WAAW;AAAA,UACd,SAAS,SAAS,QAAQ,KAAK,IAAI;AAAA,QACrC;AAEA,aAAK,cAAc;AAAA,UACjB,MAAM,YAAY,KAAK,KAAK,IAAI;AAAA,UAChC,KAAK,YAAY,IAAI,KAAK,IAAI;AAAA,QAChC;AAEA,aAAK,cAAc;AAAA,UACjB,KAAK,YAAY,IAAI,KAAK,IAAI;AAAA,UAC9B,QAAQ,YAAY,OAAO,KAAK,IAAI;AAAA,UACpC,QAAQ,YAAY,OAAO,KAAK,IAAI;AAAA,UACpC,QAAQ,YAAY,OAAO,KAAK,IAAI;AAAA,UACpC,MAAM,YAAY,KAAK,KAAK,IAAI;AAAA,UAChC,aAAa;AAAA,YACX,QAAQ,YAAY,YAAY,OAAO,KAAK,IAAI;AAAA,UAClD;AAAA,QACF;AAEA,aAAK,QAAQ;AAAA,UACX,QAAQ,MAAM,OAAO,KAAK,IAAI;AAAA,UAC9B,KAAK,MAAM,IAAI,KAAK,IAAI;AAAA,UACxB,MAAM,MAAM,KAAK,KAAK,IAAI;AAAA,UAC1B,QAAQ,MAAM,OAAO,KAAK,IAAI;AAAA,QAChC;AAEA,aAAK,WAAW;AAAA,UACd,MAAM,SAAS,KAAK,KAAK,IAAI;AAAA,QAC/B;AAEA,aAAK,SAAS;AAAA,UACZ,KAAK,OAAO,IAAI,KAAK,IAAI;AAAA,UACzB,MAAM,OAAO,KAAK,KAAK,IAAI;AAAA,UAC3B,QAAQ,OAAO,OAAO,KAAK,IAAI;AAAA,UAC/B,UAAU;AAAA,YACR,MAAM,OAAO,SAAS,KAAK,KAAK,IAAI;AAAA,YACpC,KAAK,OAAO,SAAS,IAAI,KAAK,IAAI;AAAA,UACpC;AAAA,UACA,QAAQ,OAAO,OAAO,KAAK,IAAI;AAAA,QACjC;AAEA,aAAK,cAAc;AAAA,UACjB,QAAQ,YAAY,OAAO,KAAK,IAAI;AAAA,UACpC,KAAK,YAAY,IAAI,KAAK,IAAI;AAAA,UAC9B,QAAQ,YAAY,OAAO,KAAK,IAAI;AAAA,UACpC,MAAM,YAAY,KAAK,KAAK,IAAI;AAAA,QAClC;AAEA,aAAK,YAAY;AAAA,UACf,QAAQ,UAAU,OAAO,KAAK,IAAI;AAAA,UAClC,KAAK,UAAU,IAAI,KAAK,IAAI;AAAA,UAC5B,QAAQ,UAAU,OAAO,KAAK,IAAI;AAAA,UAClC,MAAM,UAAU,KAAK,KAAK,IAAI;AAAA,QAChC;AAEA,aAAK,WAAW;AAAA,UACd,SAAS;AAAA,YACP,QAAQ;AAAA,cACN,KAAK,SAAS,QAAQ,OAAO,IAAI,KAAK,IAAI;AAAA,YAC5C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiBA,MAAM,IAAI,KAAK,SAAS,UAAU;AAChC,cAAM,EAAE,OAAO,EAAE,MAAM,QAAQ,GAAG,QAAQ,GAAG,KAAK,IAAI;AAEtD,cAAM,aAAa,uBAAuB,MAAM,GAAG;AAEnD,YAAI;AACJ,YAAI,WAAW,SAAS;AACtB,uBAAa,MAAM,KAAK,YAAY,OAAO;AAAA,YACzC,GAAG;AAAA,YACH,SAAS,WAAW;AAAA,YACpB,MAAM,KAAK,SAAS,UAAU,KAAK,WAAW,OAAO;AAAA,UACvD,CAAC;AAAA,QACH,WAAW,WAAW,SAAS,WAAW,MAAM;AAC9C,uBAAa,MAAM,KAAK,YAAY,OAAO;AAAA,YACzC,GAAG;AAAA,YACH,OAAO,GAAG,WAAW,KAAK,IAAI,WAAW,IAAI;AAAA,YAC7C,MAAM,KAAK,SAAS,UAAU,KAAK,WAAW,OAAO;AAAA,UACvD,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AAGA,YAAI,UAAU;AACZ,mBAAS,UAAU;AAAA,QACrB;AAEA,cAAM,SAAS,KAAK,SAAS,WAAW,WAAW,WAAW;AAC9D,YAAI,CAAC,QAAQ;AACX,uBAAa,MAAM,KAAK;AAAA,YACtB;AAAA,YACA,EAAE,UAAU,KAAK,SAAS,SAAS,KAAK,WAAW,OAAU;AAAA,YAC7D,OAAO,sBAAsB;AAE3B,kBAAI,UAAU;AACZ,yBAAS,iBAAiB;AAAA,cAC5B;AAGA,kBAAI,UAAU,OAAO,SAAS;AAC5B,uBAAO;AAAA,cACT;AAEA,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAEA,YAAI,UAAU,OAAO,SAAS;AAC5B,uBAAa,MAAM,KAAK,YAAY,OAAO,WAAW,EAAE;AAAA,QAC1D;AAGA,YAAI,UAAU;AACZ,mBAAS,UAAU;AAAA,QACrB;AAEA,YAAI,WAAW,WAAW,UAAU;AAClC,gBAAM,IAAI,MAAM,sBAAsB,WAAW,KAAK,EAAE;AAAA,QAC1D;AAEA,eAAO,UAAU,WAAW,QAAQ,CAAC,UAAU;AAC7C,cACE,OAAO,UAAU,aAChB,MAAM,WAAW,QAAQ,KAAK,MAAM,WAAW,OAAO,IACvD;AACA,mBAAO,KAAK,gBACR,iBAAiB,EAAE,KAAK,OAAO,OAAO,KAAK,MAAM,CAAC,IAClD;AAAA,UACN;AACA,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcA,MAAM,QAAQ,OAAO,SAAS;AAC5B,cAAM,EAAE,MAAM,SAAS,UAAU,IAAI;AAErC,YAAI;AACJ,YAAI,iBAAiB,KAAK;AACxB,gBAAM;AAAA,QACR,OAAO;AACL,gBAAM,IAAI;AAAA,YACR,MAAM,WAAW,GAAG,IAAI,MAAM,MAAM,CAAC,IAAI;AAAA,YACzC,QAAQ,SAAS,GAAG,IAAI,UAAU,GAAG,OAAO;AAAA,UAC9C;AAAA,QACF;AAEA,cAAM,EAAE,SAAS,OAAO,SAAS,CAAC,GAAG,KAAK,IAAI;AAE9C,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACjD,cAAI,aAAa,OAAO,KAAK,KAAK;AAAA,QACpC;AAEA,cAAM,UAAU;AAAA,UACd,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB;AACA,YAAI,MAAM;AACR,kBAAQ,eAAe,IAAI,UAAU,IAAI;AAAA,QAC3C;AACA,YAAI,QAAQ,SAAS;AACnB,qBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,OAAO,GAAG;AAC1D,oBAAQ,GAAG,IAAI;AAAA,UACjB;AAAA,QACF;AAEA,YAAI,OAAO;AACX,YAAI,gBAAgB,UAAU;AAC5B,iBAAO;AAEP,iBAAO,QAAQ,cAAc;AAAA,QAC/B,WAAW,MAAM;AACf,iBAAO,KAAK,UAAU,IAAI;AAAA,QAC5B;AAEA,cAAMC,QAAO;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,cAAM,cACJ,WAAW,QACP,CAACC,cAAaA,UAAS,WAAW,OAAOA,UAAS,UAAU,MAC5D,CAACA,cAAaA,UAAS,WAAW;AAIxC,cAAM,SAAS,KAAK;AACpB,cAAM,WAAW,MAAM,qBAAqB,YAAY,OAAO,KAAKD,KAAI,GAAG;AAAA,UACzE;AAAA,QACF,CAAC;AAED,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,UAAU,IAAI,QAAQ,KAAKA,KAAI;AACrC,gBAAM,eAAe,MAAM,SAAS,KAAK;AACzC,gBAAM,IAAI;AAAA,YACR,cAAc,GAAG,uBAAuB,SAAS,MAAM,IAAI,SAAS,UAAU,KAAK,YAAY;AAAA,YAC/F;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcA,OAAO,OAAO,KAAK,SAAS;AAC1B,cAAM,EAAE,MAAM,QAAQ,GAAG,KAAK,IAAI;AAElC,cAAM,aAAa,uBAAuB,MAAM,GAAG;AAEnD,YAAI;AACJ,YAAI,WAAW,SAAS;AACtB,uBAAa,MAAM,KAAK,YAAY,OAAO;AAAA,YACzC,GAAG;AAAA,YACH,SAAS,WAAW;AAAA,UACtB,CAAC;AAAA,QACH,WAAW,WAAW,SAAS,WAAW,MAAM;AAC9C,uBAAa,MAAM,KAAK,YAAY,OAAO;AAAA,YACzC,GAAG;AAAA,YACH,OAAO,GAAG,WAAW,KAAK,IAAI,WAAW,IAAI;AAAA,UAC/C,CAAC;AAAA,QACH,OAAO;AACL,gBAAM,IAAI,MAAM,kCAAkC;AAAA,QACpD;AAEA,YAAI,WAAW,QAAQ,WAAW,KAAK,QAAQ;AAC7C,gBAAM,SAAS,qBAAqB;AAAA,YAClC,KAAK,WAAW,KAAK;AAAA,YACrB,OAAO,KAAK;AAAA,YACZ,GAAI,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;AAAA,UAC1C,CAAC;AAED,iBAAO,oBAAoB,MAAM;AAAA,QACnC,OAAO;AACL,gBAAM,IAAI,MAAM,uCAAuC;AAAA,QACzD;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaA,OAAO,SAAS,UAAU;AACxB,cAAM,WAAW,MAAM,SAAS;AAChC,cAAM,SAAS;AACf,YAAI,SAAS,MAAM;AACjB,gBAAM,WAAW,MACf,KAAK,QAAQ,SAAS,MAAM,EAAE,QAAQ,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC;AACrE,iBAAO,KAAK,SAAS,QAAQ;AAAA,QAC/B;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkBA,MAAM,KAAK,YAAY,SAAS,MAAM;AACpC,cAAM,EAAE,GAAG,IAAI;AACf,YAAI,CAAC,IAAI;AACP,gBAAM,IAAI,MAAM,oBAAoB;AAAA,QACtC;AAEA,YACE,WAAW,WAAW,eACtB,WAAW,WAAW,YACtB,WAAW,WAAW,YACtB;AACA,iBAAO;AAAA,QACT;AAGA,cAAM,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAEtE,cAAM,WAAY,WAAW,QAAQ,YAAa;AAElD,YAAI,oBAAoB,MAAM,KAAK,YAAY,IAAI,EAAE;AAErD,eACE,kBAAkB,WAAW,eAC7B,kBAAkB,WAAW,YAC7B,kBAAkB,WAAW,YAC7B;AAEA,cAAI,QAAS,MAAM,KAAK,iBAAiB,MAAO,MAAM;AACpD;AAAA,UACF;AAEA,gBAAM,MAAM,QAAQ;AACpB,8BAAoB,MAAM,KAAK,YAAY,IAAI,WAAW,EAAE;AAAA,QAE9D;AAEA,YAAI,kBAAkB,WAAW,UAAU;AACzC,gBAAM,IAAI,MAAM,sBAAsB,kBAAkB,KAAK,EAAE;AAAA,QACjE;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AACjB,WAAO,QAAQ,kBAAkB;AACjC,WAAO,QAAQ,wBAAwB;AAAA;AAAA;", "names": ["require_stream", "init", "init", "response"]}