export interface ZodiacSign {
  name: string;
  symbol: string;
  element: string;
  dates: string;
  traits: string[];
  mythology: string;
  planet: string;
  color: string;
  gemstone: string;
}

export const zodiacSigns: Record<string, ZodiacSign> = {
  aries: {
    name: '<PERSON><PERSON>',
    symbol: '♈',
    element: 'Fire',
    dates: 'March 21 - April 19',
    traits: ['Courageous', 'Determined', 'Confident', 'Enthusiastic', 'Optimistic', 'Honest', 'Passionate'],
    mythology: 'The Ram - Symbol of leadership and new beginnings, connected to the Golden Fleece of Greek mythology.',
    planet: 'Mars',
    color: '#FF6B6B',
    gemstone: 'Diamond'
  },
  taurus: {
    name: '<PERSON><PERSON>',
    symbol: '♉',
    element: 'Earth',
    dates: 'April 20 - May 20',
    traits: ['Reliable', 'Patient', 'Practical', 'Devoted', 'Responsible', 'Stable'],
    mythology: 'The Bull - Connected to <PERSON> who transformed into a bull to win Europa\'s heart.',
    planet: 'Venus',
    color: '#4ECDC4',
    gemstone: 'Emerald'
  },
  gemini: {
    name: '<PERSON>',
    symbol: '♊',
    element: 'Air',
    dates: 'May 21 - June 20',
    traits: ['Gentle', 'Affectionate', 'Curious', 'Adaptable', 'Quick-learner', 'Witty'],
    mythology: 'The Twins - Castor and Pollux, representing duality and communication.',
    planet: 'Mercury',
    color: '#FFE66D',
    gemstone: 'Pearl'
  },
  cancer: {
    name: 'Cancer',
    symbol: '♋',
    element: 'Water',
    dates: 'June 21 - July 22',
    traits: ['Tenacious', 'Highly imaginative', 'Loyal', 'Emotional', 'Sympathetic', 'Persuasive'],
    mythology: 'The Crab - Sent by Hera to distract Hercules during his battle with Hydra.',
    planet: 'Moon',
    color: '#A8E6CF',
    gemstone: 'Ruby'
  },
  leo: {
    name: 'Leo',
    symbol: '♌',
    element: 'Fire',
    dates: 'July 23 - August 22',
    traits: ['Creative', 'Passionate', 'Generous', 'Warm-hearted', 'Cheerful', 'Humorous'],
    mythology: 'The Lion - The Nemean Lion defeated by Hercules as his first labor.',
    planet: 'Sun',
    color: '#FFD93D',
    gemstone: 'Peridot'
  },
  virgo: {
    name: 'Virgo',
    symbol: '♍',
    element: 'Earth',
    dates: 'August 23 - September 22',
    traits: ['Loyal', 'Analytical', 'Kind', 'Hardworking', 'Practical'],
    mythology: 'The Maiden - Associated with Persephone and the harvest season.',
    planet: 'Mercury',
    color: '#95E1D3',
    gemstone: 'Sapphire'
  },
  libra: {
    name: 'Libra',
    symbol: '♎',
    element: 'Air',
    dates: 'September 23 - October 22',
    traits: ['Cooperative', 'Diplomatic', 'Gracious', 'Fair-minded', 'Social'],
    mythology: 'The Scales - Symbol of justice and balance, held by Themis, goddess of divine law.',
    planet: 'Venus',
    color: '#F38BA8',
    gemstone: 'Opal'
  },
  scorpio: {
    name: 'Scorpio',
    symbol: '♏',
    element: 'Water',
    dates: 'October 23 - November 21',
    traits: ['Resourceful', 'Brave', 'Passionate', 'Stubborn', 'True friend'],
    mythology: 'The Scorpion - Sent by Gaia to kill Orion, representing transformation and rebirth.',
    planet: 'Pluto',
    color: '#C77DFF',
    gemstone: 'Topaz'
  },
  sagittarius: {
    name: 'Sagittarius',
    symbol: '♐',
    element: 'Fire',
    dates: 'November 22 - December 21',
    traits: ['Generous', 'Idealistic', 'Great sense of humor', 'Adventurous'],
    mythology: 'The Archer - Centaur Chiron, wise teacher and healer of heroes.',
    planet: 'Jupiter',
    color: '#7209B7',
    gemstone: 'Turquoise'
  },
  capricorn: {
    name: 'Capricorn',
    symbol: '♑',
    element: 'Earth',
    dates: 'December 22 - January 19',
    traits: ['Responsible', 'Disciplined', 'Self-control', 'Good managers'],
    mythology: 'The Sea-Goat - Pan, who transformed into a goat-fish to escape Typhon.',
    planet: 'Saturn',
    color: '#2A9D8F',
    gemstone: 'Garnet'
  },
  aquarius: {
    name: 'Aquarius',
    symbol: '♒',
    element: 'Air',
    dates: 'January 20 - February 18',
    traits: ['Progressive', 'Original', 'Independent', 'Humanitarian'],
    mythology: 'The Water Bearer - Ganymede, cupbearer to the gods, bringing divine knowledge.',
    planet: 'Uranus',
    color: '#264653',
    gemstone: 'Amethyst'
  },
  pisces: {
    name: 'Pisces',
    symbol: '♓',
    element: 'Water',
    dates: 'February 19 - March 20',
    traits: ['Compassionate', 'Artistic', 'Intuitive', 'Gentle', 'Wise', 'Musical'],
    mythology: 'The Fishes - Aphrodite and Eros transformed into fish to escape Typhon.',
    planet: 'Neptune',
    color: '#E76F51',
    gemstone: 'Aquamarine'
  }
};

export const getZodiacByDate = (birthDate: Date): string => {
  const month = birthDate.getMonth() + 1;
  const day = birthDate.getDate();

  if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'aries';
  if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'taurus';
  if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'gemini';
  if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'cancer';
  if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'leo';
  if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'virgo';
  if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'libra';
  if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'scorpio';
  if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'sagittarius';
  if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'capricorn';
  if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'aquarius';
  return 'pisces';
};

export const getDailyInstructions = (zodiacSign: string): string[] => {
  const instructions: Record<string, string[]> = {
    aries: [
      'Channel your natural leadership energy into a meaningful project today.',
      'Take initiative in a situation that has been stagnant.',
      'Practice patience - not everything needs to be rushed.',
      'Your courage will inspire others around you.'
    ],
    taurus: [
      'Focus on building something lasting and valuable today.',
      'Trust your instincts when making financial decisions.',
      'Take time to appreciate the beauty around you.',
      'Your steady approach will yield the best results.'
    ],
    gemini: [
      'Engage in meaningful conversations that expand your perspective.',
      'Use your communication skills to bridge differences.',
      'Learn something new that sparks your curiosity.',
      'Balance your social energy with quiet reflection.'
    ],
    cancer: [
      'Trust your intuitive feelings about people and situations.',
      'Nurture your relationships with genuine care.',
      'Create a peaceful sanctuary in your personal space.',
      'Your emotional wisdom guides you toward the right path.'
    ],
    leo: [
      'Let your natural charisma shine in social situations.',
      'Express your creativity boldly and without fear.',
      'Encourage others to pursue their dreams.',
      'Remember that true leadership serves others.'
    ],
    virgo: [
      'Pay attention to the details others might overlook.',
      'Organize your environment to support your goals.',
      'Offer practical help to someone in need.',
      'Your analytical skills reveal important insights today.'
    ],
    libra: [
      'Seek harmony in all your interactions today.',
      'Make decisions that benefit everyone involved.',
      'Appreciate art, beauty, and aesthetic experiences.',
      'Your diplomatic nature helps resolve conflicts.'
    ],
    scorpio: [
      'Trust your deep intuition about hidden truths.',
      'Transform a challenging situation into an opportunity.',
      'Dive deep into subjects that fascinate you.',
      'Your intensity and focus achieve remarkable results.'
    ],
    sagittarius: [
      'Explore new philosophies or belief systems.',
      'Plan an adventure that expands your horizons.',
      'Share your wisdom and experiences with others.',
      'Your optimism lights the way for those around you.'
    ],
    capricorn: [
      'Set realistic goals and work steadily toward them.',
      'Your discipline and persistence pay off today.',
      'Take on responsibilities that showcase your reliability.',
      'Build foundations that will support your future success.'
    ],
    aquarius: [
      'Think outside conventional boundaries.',
      'Connect with like-minded individuals who share your vision.',
      'Use technology or innovation to solve problems.',
      'Your unique perspective offers valuable solutions.'
    ],
    pisces: [
      'Trust your dreams and intuitive insights.',
      'Express your emotions through creative outlets.',
      'Show compassion to those who are struggling.',
      'Your empathy and understanding heal others.'
    ]
  };

  return instructions[zodiacSign] || instructions.pisces;
};
