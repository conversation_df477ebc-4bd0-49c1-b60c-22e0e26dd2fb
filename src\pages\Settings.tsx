import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Settings as SettingsIcon, Globe, Bell, Moon, Sun, Save, Trash2 } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useStore } from '../store/useStore';

const Settings: React.FC = () => {
  const { updateUserProfile, logout } = useAuth();
  const { user, currentLanguage, setLanguage, setLoading, isLoading, setError, clearUserData } = useStore();
  const [settings, setSettings] = useState({
    language: user?.preferences?.language || 'en',
    notifications: user?.preferences?.notifications ?? true,
    theme: user?.preferences?.theme || 'dark'
  });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));

    // Update language immediately in store
    if (key === 'language') {
      setLanguage(value);
    }
  };

  const handleSave = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      await updateUserProfile({
        ...user,
        preferences: {
          ...user.preferences,
          ...settings
        }
      });
    } catch (error: any) {
      setError(error.message || 'Failed to update settings');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAccount = async () => {
    // In a real app, you'd implement account deletion
    // For now, we'll just logout and clear data
    try {
      await logout();
      clearUserData();
    } catch (error: any) {
      setError(error.message || 'Failed to delete account');
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="text-center mb-8">
          <h1 className="mythology-title text-4xl font-bold mb-4">Settings</h1>
          <p className="text-xl text-gray-400">
            Customize your cosmic experience
          </p>
        </div>

        {/* Language Settings */}
        <div className="mythology-card p-6 mb-6">
          <div className="flex items-center mb-4">
            <Globe className="w-6 h-6 text-mythology-gold mr-3" />
            <h2 className="text-xl font-semibold text-white">Language & Region</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Preferred Language
              </label>
              <select
                value={settings.language}
                onChange={(e) => handleSettingChange('language', e.target.value)}
                className="w-full px-4 py-3 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-mythology-gold focus:border-transparent"
              >
                <option value="en">English</option>
                <option value="si">සිංහල (Sinhala)</option>
              </select>
              <p className="text-sm text-gray-400 mt-2">
                All content will be translated to your preferred language using AI
              </p>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="mythology-card p-6 mb-6">
          <div className="flex items-center mb-4">
            <Bell className="w-6 h-6 text-mythology-gold mr-3" />
            <h2 className="text-xl font-semibold text-white">Notifications</h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-medium">Daily Horoscope Reminders</h3>
                <p className="text-sm text-gray-400">Get notified when your daily reading is ready</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.notifications}
                  onChange={(e) => handleSettingChange('notifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-mythology-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-mythology-gold"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-medium">Weekly Insights</h3>
                <p className="text-sm text-gray-400">Receive weekly cosmic insights and guidance</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.notifications}
                  onChange={(e) => handleSettingChange('notifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-dark-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-mythology-gold/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-mythology-gold"></div>
              </label>
            </div>
          </div>
        </div>

        {/* Theme Settings */}
        <div className="mythology-card p-6 mb-6">
          <div className="flex items-center mb-4">
            <Moon className="w-6 h-6 text-mythology-gold mr-3" />
            <h2 className="text-xl font-semibold text-white">Appearance</h2>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Theme
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => handleSettingChange('theme', 'dark')}
                  className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                    settings.theme === 'dark'
                      ? 'border-mythology-gold bg-mythology-gold/20'
                      : 'border-dark-600 hover:border-mythology-gold/50'
                  }`}
                >
                  <Moon className="w-6 h-6 text-mythology-gold mx-auto mb-2" />
                  <p className="text-white font-medium">Dark</p>
                  <p className="text-sm text-gray-400">Current theme</p>
                </button>
                
                <button
                  onClick={() => handleSettingChange('theme', 'light')}
                  className={`p-4 rounded-lg border-2 transition-all duration-300 opacity-50 cursor-not-allowed ${
                    settings.theme === 'light'
                      ? 'border-mythology-gold bg-mythology-gold/20'
                      : 'border-dark-600'
                  }`}
                  disabled
                >
                  <Sun className="w-6 h-6 text-mythology-gold mx-auto mb-2" />
                  <p className="text-white font-medium">Light</p>
                  <p className="text-sm text-gray-400">Coming soon</p>
                </button>
                
                <button
                  onClick={() => handleSettingChange('theme', 'auto')}
                  className={`p-4 rounded-lg border-2 transition-all duration-300 opacity-50 cursor-not-allowed ${
                    settings.theme === 'auto'
                      ? 'border-mythology-gold bg-mythology-gold/20'
                      : 'border-dark-600'
                  }`}
                  disabled
                >
                  <SettingsIcon className="w-6 h-6 text-mythology-gold mx-auto mb-2" />
                  <p className="text-white font-medium">Auto</p>
                  <p className="text-sm text-gray-400">Coming soon</p>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="text-center mb-8">
          <button
            onClick={handleSave}
            disabled={isLoading}
            className="mythology-button px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto"
          >
            <Save size={20} />
            <span>{isLoading ? 'Saving...' : 'Save Settings'}</span>
          </button>
        </div>

        {/* Danger Zone */}
        <div className="mythology-card p-6 border-red-500/20">
          <div className="flex items-center mb-4">
            <Trash2 className="w-6 h-6 text-red-400 mr-3" />
            <h2 className="text-xl font-semibold text-red-400">Danger Zone</h2>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-medium">Delete Account</h3>
                <p className="text-sm text-gray-400">
                  Permanently delete your account and all associated data
                </p>
              </div>
              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete Account
              </button>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="mythology-card p-6 max-w-md w-full"
            >
              <h3 className="text-xl font-semibold text-red-400 mb-4">Confirm Account Deletion</h3>
              <p className="text-gray-300 mb-6">
                Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.
              </p>
              <div className="flex space-x-4">
                <button
                  onClick={handleDeleteAccount}
                  className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors"
                >
                  Yes, Delete Account
                </button>
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1 border border-gray-600 text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default Settings;
