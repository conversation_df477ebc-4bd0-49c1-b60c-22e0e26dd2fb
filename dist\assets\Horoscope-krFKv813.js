import{u as v,r as h,j as e,m,z as d,S as p,C as S,a as w}from"./index-QubfbT_B.js";import{g as k}from"./apis-tgNVP_Vo.js";import{R as M}from"./refresh-cw-BMeRD5eS.js";import{f as n}from"./format-CBpsKyOP.js";const E=()=>{const{user:i,horoscopes:r,addHoroscope:j,setLoading:x,isLoading:y,setError:g}=v(),[l,b]=h.useState("daily"),[o,N]=h.useState((i==null?void 0:i.zodiacSign)||"aries"),u=[{key:"daily",label:"Daily",icon:e.jsx(S,{size:20})},{key:"weekly",label:"Weekly",icon:e.jsx(w,{size:20})},{key:"monthly",label:"Monthly",icon:e.jsx(p,{size:20})}],f=async()=>{if(o){x(!0),g(null);try{const s=await k(o,l,i),a={id:Date.now().toString(),zodiacSign:o,period:l,content:s,date:n(new Date,"yyyy-MM-dd")};j(a)}catch(s){g(s.message||"Failed to generate horoscope")}finally{x(!1)}}},c=(()=>{const s=n(new Date,"yyyy-MM-dd");return r.find(a=>a.zodiacSign===o&&a.period===l&&a.date===s)})(),t=d[o];return e.jsx("div",{className:"max-w-4xl mx-auto",children:e.jsxs(m.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.6},children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h1",{className:"mythology-title text-4xl font-bold mb-4",children:"Your Horoscope"}),e.jsx("p",{className:"text-xl text-gray-400",children:"Discover what the stars have aligned for you"})]}),e.jsxs("div",{className:"mythology-card p-6 mb-8",children:[e.jsx("h2",{className:"text-2xl font-semibold mb-4 text-mythology-gold",children:"Select Your Sign"}),e.jsx("div",{className:"grid grid-cols-3 md:grid-cols-6 lg:grid-cols-12 gap-3",children:Object.entries(d).map(([s,a])=>e.jsxs("button",{onClick:()=>N(s),className:`p-3 rounded-lg border-2 transition-all duration-300 ${o===s?"border-mythology-gold bg-mythology-gold/20 text-mythology-gold":"border-dark-600 hover:border-mythology-gold/50 text-gray-400 hover:text-gray-300"}`,children:[e.jsx("div",{className:"text-2xl mb-1",children:a.symbol}),e.jsx("div",{className:"text-xs font-medium",children:a.name})]},s))})]}),e.jsxs("div",{className:"mythology-card p-6 mb-8",children:[e.jsx("h2",{className:"text-2xl font-semibold mb-4 text-mythology-gold",children:"Reading Period"}),e.jsx("div",{className:"flex flex-wrap gap-4",children:u.map(s=>e.jsxs("button",{onClick:()=>b(s.key),className:`flex items-center space-x-2 px-6 py-3 rounded-lg border-2 transition-all duration-300 ${l===s.key?"border-mythology-gold bg-mythology-gold/20 text-mythology-gold":"border-dark-600 hover:border-mythology-gold/50 text-gray-400 hover:text-gray-300"}`,children:[s.icon,e.jsx("span",{className:"font-medium",children:s.label})]},s.key))})]}),t&&e.jsxs("div",{className:"mythology-card p-6 mb-8",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"text-4xl mr-4",children:t.symbol}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-2xl font-semibold text-mythology-gold",children:t.name}),e.jsx("p",{className:"text-gray-400",children:t.dates})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-mythology-gold font-medium",children:"Element:"})," ",t.element]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-mythology-gold font-medium",children:"Planet:"})," ",t.planet]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-mythology-gold font-medium",children:"Gemstone:"})," ",t.gemstone]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-mythology-gold font-medium",children:"Color:"}),e.jsx("span",{className:"inline-block w-4 h-4 rounded-full ml-2 align-middle",style:{backgroundColor:t.color}})]})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-mythology-gold font-medium mb-2",children:"Mythology:"}),e.jsx("p",{className:"text-gray-300 text-sm",children:t.mythology})]})]}),e.jsx("div",{className:"text-center mb-8",children:e.jsx("button",{onClick:f,disabled:y,className:"mythology-button px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto",children:y?e.jsxs(e.Fragment,{children:[e.jsx(M,{className:"animate-spin",size:20}),e.jsx("span",{children:"Consulting the Stars..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(p,{size:20}),e.jsxs("span",{children:["Get My ",l.charAt(0).toUpperCase()+l.slice(1)," Reading"]})]})})}),c&&e.jsxs(m.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mythology-card p-8",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsxs("h3",{className:"text-2xl font-semibold text-mythology-gold mb-2",children:[t.name," ",l.charAt(0).toUpperCase()+l.slice(1)," Reading"]}),e.jsx("p",{className:"text-gray-400",children:n(new Date(c.date),"MMMM d, yyyy")})]}),e.jsx("div",{className:"prose prose-invert max-w-none",children:e.jsx("p",{className:"text-gray-300 leading-relaxed text-lg whitespace-pre-line",children:c.content})})]}),r.length>0&&e.jsxs("div",{className:"mt-12",children:[e.jsx("h2",{className:"text-2xl font-semibold mb-6 text-mythology-gold",children:"Recent Readings"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:r.slice(0,4).map(s=>e.jsxs(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mythology-card p-6",children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx("span",{className:"text-2xl mr-3",children:d[s.zodiacSign].symbol}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-white",children:d[s.zodiacSign].name}),e.jsxs("p",{className:"text-sm text-gray-400",children:[s.period," • ",n(new Date(s.date),"MMM d")]})]})]}),e.jsx("p",{className:"text-gray-300 text-sm line-clamp-3",children:s.content})]},s.id))})]})]})})};export{E as default};
