import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Types
export interface User {
  uid: string;
  email: string;
  displayName?: string;
  zodiacSign?: string;
  birthDate?: string;
  preferences?: {
    language: string;
    notifications: boolean;
    theme: string;
  };
}

export interface Horoscope {
  id: string;
  zodiacSign: string;
  period: 'daily' | 'weekly' | 'monthly';
  content: string;
  date: string;
  imageUrl?: string;
}

export interface FutureReading {
  id: string;
  userId: string;
  content: string;
  date: string;
  imageUrl?: string;
  category: string;
}

export interface AppState {
  // User state
  user: User | null;
  isAuthenticated: boolean;
  
  // App state
  currentLanguage: string;
  isLoading: boolean;
  error: string | null;
  
  // Content state
  horoscopes: Horoscope[];
  futureReadings: FutureReading[];
  dailyInstructions: string | null;
  
  // Actions
  setUser: (user: User | null) => void;
  setAuthenticated: (isAuth: boolean) => void;
  setLanguage: (language: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  addHoroscope: (horoscope: Horoscope) => void;
  addFutureReading: (reading: FutureReading) => void;
  setDailyInstructions: (instructions: string) => void;
  clearUserData: () => void;
}

export const useStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      currentLanguage: 'en',
      isLoading: false,
      error: null,
      horoscopes: [],
      futureReadings: [],
      dailyInstructions: null,

      // Actions
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      
      setAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
      
      setLanguage: (currentLanguage) => set({ currentLanguage }),
      
      setLoading: (isLoading) => set({ isLoading }),
      
      setError: (error) => set({ error }),
      
      addHoroscope: (horoscope) => set((state) => ({
        horoscopes: [horoscope, ...state.horoscopes.slice(0, 9)] // Keep last 10
      })),
      
      addFutureReading: (reading) => set((state) => ({
        futureReadings: [reading, ...state.futureReadings.slice(0, 19)] // Keep last 20
      })),
      
      setDailyInstructions: (dailyInstructions) => set({ dailyInstructions }),
      
      clearUserData: () => set({
        user: null,
        isAuthenticated: false,
        horoscopes: [],
        futureReadings: [],
        dailyInstructions: null,
        error: null,
      }),
    }),
    {
      name: 'kubera-storage',
      partialize: (state) => ({
        user: state.user,
        currentLanguage: state.currentLanguage,
        horoscopes: state.horoscopes,
        futureReadings: state.futureReadings,
      }),
    }
  )
);
