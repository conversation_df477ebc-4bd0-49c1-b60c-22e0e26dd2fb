import{g as kr}from"./index-QubfbT_B.js";function k(n,e,t,s,r){if(typeof e=="function"?n!==e||!0:!e.has(n))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(n,t),t}function c(n,e,t,s){if(t==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?n!==e||!s:!e.has(n))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?s:t==="a"?s.call(n):s?s.value:e.get(n)}let cn=function(){const{crypto:n}=globalThis;if(n!=null&&n.randomUUID)return cn=n.randomUUID.bind(n),n.randomUUID();const e=new Uint8Array(1),t=n?()=>n.getRandomValues(e)[0]:()=>Math.random()*255&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,s=>(+s^t()&15>>+s/4).toString(16))};function jt(n){return typeof n=="object"&&n!==null&&("name"in n&&n.name==="AbortError"||"message"in n&&String(n.message).includes("FetchRequestCanceledException"))}const Dt=n=>{if(n instanceof Error)return n;if(typeof n=="object"&&n!==null){try{if(Object.prototype.toString.call(n)==="[object Error]"){const e=new Error(n.message,n.cause?{cause:n.cause}:{});return n.stack&&(e.stack=n.stack),n.cause&&!e.cause&&(e.cause=n.cause),n.name&&(e.name=n.name),e}}catch{}try{return new Error(JSON.stringify(n))}catch{}}return new Error(n)};class O extends Error{}class J extends O{constructor(e,t,s,r){super(`${J.makeMessage(e,t,s)}`),this.status=e,this.headers=r,this.requestID=r==null?void 0:r.get("x-request-id"),this.error=t;const a=t;this.code=a==null?void 0:a.code,this.param=a==null?void 0:a.param,this.type=a==null?void 0:a.type}static makeMessage(e,t,s){const r=t!=null&&t.message?typeof t.message=="string"?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&r?`${e} ${r}`:e?`${e} status code (no body)`:r||"(no status code or body)"}static generate(e,t,s,r){if(!e||!r)return new ct({message:s,cause:Dt(t)});const a=t==null?void 0:t.error;return e===400?new ln(e,a,s,r):e===401?new un(e,a,s,r):e===403?new dn(e,a,s,r):e===404?new hn(e,a,s,r):e===409?new fn(e,a,s,r):e===422?new mn(e,a,s,r):e===429?new pn(e,a,s,r):e>=500?new gn(e,a,s,r):new J(e,a,s,r)}}class Q extends J{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class ct extends J{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class Yt extends ct{constructor({message:e}={}){super({message:e??"Request timed out."})}}class ln extends J{}class un extends J{}class dn extends J{}class hn extends J{}class fn extends J{}class mn extends J{}class pn extends J{}class gn extends J{}class _n extends O{constructor(){super("Could not parse response content as the length limit was reached")}}class wn extends O{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}const Pr=/^[a-z][a-z0-9+.-]*:/i,Tr=n=>Pr.test(n);function Mr(n){return typeof n!="object"?{}:n??{}}function Nr(n){if(!n)return!0;for(const e in n)return!1;return!0}function Fr(n,e){return Object.prototype.hasOwnProperty.call(n,e)}function gt(n){return n!=null&&typeof n=="object"&&!Array.isArray(n)}const qr=(n,e)=>{if(typeof e!="number"||!Number.isInteger(e))throw new O(`${n} must be an integer`);if(e<0)throw new O(`${n} must be a positive integer`);return e},Lr=n=>{try{return JSON.parse(n)}catch{return}},Fe=n=>new Promise(e=>setTimeout(e,n)),tt={off:0,error:200,warn:300,info:400,debug:500},bs=(n,e,t)=>{if(n){if(Fr(tt,n))return n;H(t).warn(`${e} was set to ${JSON.stringify(n)}, expected one of ${JSON.stringify(Object.keys(tt))}`)}};function ve(){}function je(n,e,t){return!e||tt[n]>tt[t]?ve:e[n].bind(e)}const Br={error:ve,warn:ve,info:ve,debug:ve};let Ss=new WeakMap;function H(n){const e=n.logger,t=n.logLevel??"off";if(!e)return Br;const s=Ss.get(e);if(s&&s[0]===t)return s[1];const r={error:je("error",e,t),warn:je("warn",e,t),info:je("info",e,t),debug:je("debug",e,t)};return Ss.set(e,[t,r]),r}const ce=n=>(n.options&&(n.options={...n.options},delete n.options.headers),n.headers&&(n.headers=Object.fromEntries((n.headers instanceof Headers?[...n.headers]:Object.entries(n.headers)).map(([e,t])=>[e,e.toLowerCase()==="authorization"||e.toLowerCase()==="cookie"||e.toLowerCase()==="set-cookie"?"***":t]))),"retryOfRequestLogID"in n&&(n.retryOfRequestLogID&&(n.retryOf=n.retryOfRequestLogID),delete n.retryOfRequestLogID),n),ge="5.3.0",jr=()=>typeof window<"u"&&typeof window.document<"u"&&typeof navigator<"u";function Dr(){return typeof Deno<"u"&&Deno.build!=null?"deno":typeof EdgeRuntime<"u"?"edge":Object.prototype.toString.call(typeof globalThis.process<"u"?globalThis.process:0)==="[object process]"?"node":"unknown"}const Ur=()=>{var t;const n=Dr();if(n==="deno")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ge,"X-Stainless-OS":As(Deno.build.os),"X-Stainless-Arch":xs(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:((t=Deno.version)==null?void 0:t.deno)??"unknown"};if(typeof EdgeRuntime<"u")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ge,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if(n==="node")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ge,"X-Stainless-OS":As(globalThis.process.platform??"unknown"),"X-Stainless-Arch":xs(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};const e=Wr();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ge,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":ge,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function Wr(){if(typeof navigator>"u"||!navigator)return null;const n=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:e,pattern:t}of n){const s=t.exec(navigator.userAgent);if(s){const r=s[1]||0,a=s[2]||0,i=s[3]||0;return{browser:e,version:`${r}.${a}.${i}`}}}return null}const xs=n=>n==="x32"?"x32":n==="x86_64"||n==="x64"?"x64":n==="arm"?"arm":n==="aarch64"||n==="arm64"?"arm64":n?`other:${n}`:"unknown",As=n=>(n=n.toLowerCase(),n.includes("ios")?"iOS":n==="android"?"Android":n==="darwin"?"MacOS":n==="win32"?"Windows":n==="freebsd"?"FreeBSD":n==="openbsd"?"OpenBSD":n==="linux"?"Linux":n?`Other:${n}`:"Unknown");let vs;const Jr=()=>vs??(vs=Ur());function Hr(){if(typeof fetch<"u")return fetch;throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}function yn(...n){const e=globalThis.ReadableStream;if(typeof e>"u")throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new e(...n)}function bn(n){let e=Symbol.asyncIterator in n?n[Symbol.asyncIterator]():n[Symbol.iterator]();return yn({start(){},async pull(t){const{done:s,value:r}=await e.next();s?t.close():t.enqueue(r)},async cancel(){var t;await((t=e.return)==null?void 0:t.call(e))}})}function Sn(n){if(n[Symbol.asyncIterator])return n;const e=n.getReader();return{async next(){try{const t=await e.read();return t!=null&&t.done&&e.releaseLock(),t}catch(t){throw e.releaseLock(),t}},async return(){const t=e.cancel();return e.releaseLock(),await t,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function Xr(n){var s,r;if(n===null||typeof n!="object")return;if(n[Symbol.asyncIterator]){await((r=(s=n[Symbol.asyncIterator]()).return)==null?void 0:r.call(s));return}const e=n.getReader(),t=e.cancel();e.releaseLock(),await t}const Gr=({headers:n,body:e})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(e)}),Ut="RFC3986",Wt={RFC1738:n=>String(n).replace(/%20/g,"+"),RFC3986:n=>String(n)},Vr="RFC1738",Kr=Array.isArray,ee=(()=>{const n=[];for(let e=0;e<256;++e)n.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return n})(),_t=1024,Qr=(n,e,t,s,r)=>{if(n.length===0)return n;let a=n;if(typeof n=="symbol"?a=Symbol.prototype.toString.call(n):typeof n!="string"&&(a=String(n)),t==="iso-8859-1")return escape(a).replace(/%u[0-9a-f]{4}/gi,function(o){return"%26%23"+parseInt(o.slice(2),16)+"%3B"});let i="";for(let o=0;o<a.length;o+=_t){const l=a.length>=_t?a.slice(o,o+_t):a,m=[];for(let y=0;y<l.length;++y){let f=l.charCodeAt(y);if(f===45||f===46||f===95||f===126||f>=48&&f<=57||f>=65&&f<=90||f>=97&&f<=122||r===Vr&&(f===40||f===41)){m[m.length]=l.charAt(y);continue}if(f<128){m[m.length]=ee[f];continue}if(f<2048){m[m.length]=ee[192|f>>6]+ee[128|f&63];continue}if(f<55296||f>=57344){m[m.length]=ee[224|f>>12]+ee[128|f>>6&63]+ee[128|f&63];continue}y+=1,f=65536+((f&1023)<<10|l.charCodeAt(y)&1023),m[m.length]=ee[240|f>>18]+ee[128|f>>12&63]+ee[128|f>>6&63]+ee[128|f&63]}i+=m.join("")}return i};function zr(n){return!n||typeof n!="object"?!1:!!(n.constructor&&n.constructor.isBuffer&&n.constructor.isBuffer(n))}function Rs(n,e){if(Kr(n)){const t=[];for(let s=0;s<n.length;s+=1)t.push(e(n[s]));return t}return e(n)}const Yr=Object.prototype.hasOwnProperty,xn={brackets(n){return String(n)+"[]"},comma:"comma",indices(n,e){return String(n)+"["+e+"]"},repeat(n){return String(n)}},te=Array.isArray,Zr=Array.prototype.push,An=function(n,e){Zr.apply(n,te(e)?e:[e])},ea=Date.prototype.toISOString,j={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:Qr,encodeValuesOnly:!1,format:Ut,formatter:Wt[Ut],indices:!1,serializeDate(n){return ea.call(n)},skipNulls:!1,strictNullHandling:!1};function ta(n){return typeof n=="string"||typeof n=="number"||typeof n=="boolean"||typeof n=="symbol"||typeof n=="bigint"}const wt={};function vn(n,e,t,s,r,a,i,o,l,m,y,f,h,u,g,S,d,A){let p=n,_=A,w=0,T=!1;for(;(_=_.get(wt))!==void 0&&!T;){const E=_.get(n);if(w+=1,typeof E<"u"){if(E===w)throw new RangeError("Cyclic object value");T=!0}typeof _.get(wt)>"u"&&(w=0)}if(typeof m=="function"?p=m(e,p):p instanceof Date?p=h==null?void 0:h(p):t==="comma"&&te(p)&&(p=Rs(p,function(E){return E instanceof Date?h==null?void 0:h(E):E})),p===null){if(a)return l&&!S?l(e,j.encoder,d,"key",u):e;p=""}if(ta(p)||zr(p)){if(l){const E=S?e:l(e,j.encoder,d,"key",u);return[(g==null?void 0:g(E))+"="+(g==null?void 0:g(l(p,j.encoder,d,"value",u)))]}return[(g==null?void 0:g(e))+"="+(g==null?void 0:g(String(p)))]}const $=[];if(typeof p>"u")return $;let v;if(t==="comma"&&te(p))S&&l&&(p=Rs(p,l)),v=[{value:p.length>0?p.join(",")||null:void 0}];else if(te(m))v=m;else{const E=Object.keys(p);v=y?E.sort(y):E}const I=o?String(e).replace(/\./g,"%2E"):String(e),x=s&&te(p)&&p.length===1?I+"[]":I;if(r&&te(p)&&p.length===0)return x+"[]";for(let E=0;E<v.length;++E){const R=v[E],M=typeof R=="object"&&typeof R.value<"u"?R.value:p[R];if(i&&M===null)continue;const F=f&&o?R.replace(/\./g,"%2E"):R,L=te(p)?typeof t=="function"?t(x,F):x:x+(f?"."+F:"["+F+"]");A.set(n,w);const D=new WeakMap;D.set(wt,A),An($,vn(M,L,t,s,r,a,i,o,t==="comma"&&S&&te(p)?null:l,m,y,f,h,u,g,S,d,D))}return $}function sa(n=j){if(typeof n.allowEmptyArrays<"u"&&typeof n.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof n.encodeDotInKeys<"u"&&typeof n.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(n.encoder!==null&&typeof n.encoder<"u"&&typeof n.encoder!="function")throw new TypeError("Encoder has to be a function.");const e=n.charset||j.charset;if(typeof n.charset<"u"&&n.charset!=="utf-8"&&n.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let t=Ut;if(typeof n.format<"u"){if(!Yr.call(Wt,n.format))throw new TypeError("Unknown format option provided.");t=n.format}const s=Wt[t];let r=j.filter;(typeof n.filter=="function"||te(n.filter))&&(r=n.filter);let a;if(n.arrayFormat&&n.arrayFormat in xn?a=n.arrayFormat:"indices"in n?a=n.indices?"indices":"repeat":a=j.arrayFormat,"commaRoundTrip"in n&&typeof n.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");const i=typeof n.allowDots>"u"?n.encodeDotInKeys?!0:j.allowDots:!!n.allowDots;return{addQueryPrefix:typeof n.addQueryPrefix=="boolean"?n.addQueryPrefix:j.addQueryPrefix,allowDots:i,allowEmptyArrays:typeof n.allowEmptyArrays=="boolean"?!!n.allowEmptyArrays:j.allowEmptyArrays,arrayFormat:a,charset:e,charsetSentinel:typeof n.charsetSentinel=="boolean"?n.charsetSentinel:j.charsetSentinel,commaRoundTrip:!!n.commaRoundTrip,delimiter:typeof n.delimiter>"u"?j.delimiter:n.delimiter,encode:typeof n.encode=="boolean"?n.encode:j.encode,encodeDotInKeys:typeof n.encodeDotInKeys=="boolean"?n.encodeDotInKeys:j.encodeDotInKeys,encoder:typeof n.encoder=="function"?n.encoder:j.encoder,encodeValuesOnly:typeof n.encodeValuesOnly=="boolean"?n.encodeValuesOnly:j.encodeValuesOnly,filter:r,format:t,formatter:s,serializeDate:typeof n.serializeDate=="function"?n.serializeDate:j.serializeDate,skipNulls:typeof n.skipNulls=="boolean"?n.skipNulls:j.skipNulls,sort:typeof n.sort=="function"?n.sort:null,strictNullHandling:typeof n.strictNullHandling=="boolean"?n.strictNullHandling:j.strictNullHandling}}function na(n,e={}){let t=n;const s=sa(e);let r,a;typeof s.filter=="function"?(a=s.filter,t=a("",t)):te(s.filter)&&(a=s.filter,r=a);const i=[];if(typeof t!="object"||t===null)return"";const o=xn[s.arrayFormat],l=o==="comma"&&s.commaRoundTrip;r||(r=Object.keys(t)),s.sort&&r.sort(s.sort);const m=new WeakMap;for(let h=0;h<r.length;++h){const u=r[h];s.skipNulls&&t[u]===null||An(i,vn(t[u],u,o,l,s.allowEmptyArrays,s.strictNullHandling,s.skipNulls,s.encodeDotInKeys,s.encode?s.encoder:null,s.filter,s.sort,s.allowDots,s.serializeDate,s.format,s.formatter,s.encodeValuesOnly,s.charset,m))}const y=i.join(s.delimiter);let f=s.addQueryPrefix===!0?"?":"";return s.charsetSentinel&&(s.charset==="iso-8859-1"?f+="utf8=%26%2310003%3B&":f+="utf8=%E2%9C%93&"),y.length>0?f+y:""}function ra(n){let e=0;for(const r of n)e+=r.length;const t=new Uint8Array(e);let s=0;for(const r of n)t.set(r,s),s+=r.length;return t}let $s;function Zt(n){let e;return($s??(e=new globalThis.TextEncoder,$s=e.encode.bind(e)))(n)}let Es;function Cs(n){let e;return(Es??(e=new globalThis.TextDecoder,Es=e.decode.bind(e)))(n)}var G,V;class lt{constructor(){G.set(this,void 0),V.set(this,void 0),k(this,G,new Uint8Array),k(this,V,null)}decode(e){if(e==null)return[];const t=e instanceof ArrayBuffer?new Uint8Array(e):typeof e=="string"?Zt(e):e;k(this,G,ra([c(this,G,"f"),t]));const s=[];let r;for(;(r=aa(c(this,G,"f"),c(this,V,"f")))!=null;){if(r.carriage&&c(this,V,"f")==null){k(this,V,r.index);continue}if(c(this,V,"f")!=null&&(r.index!==c(this,V,"f")+1||r.carriage)){s.push(Cs(c(this,G,"f").subarray(0,c(this,V,"f")-1))),k(this,G,c(this,G,"f").subarray(c(this,V,"f"))),k(this,V,null);continue}const a=c(this,V,"f")!==null?r.preceding-1:r.preceding,i=Cs(c(this,G,"f").subarray(0,a));s.push(i),k(this,G,c(this,G,"f").subarray(r.index)),k(this,V,null)}return s}flush(){return c(this,G,"f").length?this.decode(`
`):[]}}G=new WeakMap,V=new WeakMap;lt.NEWLINE_CHARS=new Set([`
`,"\r"]);lt.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function aa(n,e){for(let r=e??0;r<n.length;r++){if(n[r]===10)return{preceding:r,index:r+1,carriage:!1};if(n[r]===13)return{preceding:r,index:r+1,carriage:!0}}return null}function ia(n){for(let s=0;s<n.length-1;s++){if(n[s]===10&&n[s+1]===10||n[s]===13&&n[s+1]===13)return s+2;if(n[s]===13&&n[s+1]===10&&s+3<n.length&&n[s+2]===13&&n[s+3]===10)return s+4}return-1}class ne{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*r(){if(s)throw new O("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let a=!1;try{for await(const i of oa(e,t))if(!a){if(i.data.startsWith("[DONE]")){a=!0;continue}if(i.event===null||i.event.startsWith("response.")||i.event.startsWith("transcript.")){let o;try{o=JSON.parse(i.data)}catch(l){throw console.error("Could not parse message into JSON:",i.data),console.error("From chunk:",i.raw),l}if(o&&o.error)throw new J(void 0,o.error,void 0,e.headers);yield o}else{let o;try{o=JSON.parse(i.data)}catch(l){throw console.error("Could not parse message into JSON:",i.data),console.error("From chunk:",i.raw),l}if(i.event=="error")throw new J(void 0,o.error,o.message,void 0);yield{event:i.event,data:o}}}a=!0}catch(i){if(jt(i))return;throw i}finally{a||t.abort()}}return new ne(r,t)}static fromReadableStream(e,t){let s=!1;async function*r(){const i=new lt,o=Sn(e);for await(const l of o)for(const m of i.decode(l))yield m;for(const l of i.flush())yield l}async function*a(){if(s)throw new O("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let i=!1;try{for await(const o of r())i||o&&(yield JSON.parse(o));i=!0}catch(o){if(jt(o))return;throw o}finally{i||t.abort()}}return new ne(a,t)}[Symbol.asyncIterator](){return this.iterator()}tee(){const e=[],t=[],s=this.iterator(),r=a=>({next:()=>{if(a.length===0){const i=s.next();e.push(i),t.push(i)}return a.shift()}});return[new ne(()=>r(e),this.controller),new ne(()=>r(t),this.controller)]}toReadableStream(){const e=this;let t;return yn({async start(){t=e[Symbol.asyncIterator]()},async pull(s){try{const{value:r,done:a}=await t.next();if(a)return s.close();const i=Zt(JSON.stringify(r)+`
`);s.enqueue(i)}catch(r){s.error(r)}},async cancel(){var s;await((s=t.return)==null?void 0:s.call(t))}})}}async function*oa(n,e){if(!n.body)throw e.abort(),typeof globalThis.navigator<"u"&&globalThis.navigator.product==="ReactNative"?new O("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new O("Attempted to iterate over a response with no body");const t=new la,s=new lt,r=Sn(n.body);for await(const a of ca(r))for(const i of s.decode(a)){const o=t.decode(i);o&&(yield o)}for(const a of s.flush()){const i=t.decode(a);i&&(yield i)}}async function*ca(n){let e=new Uint8Array;for await(const t of n){if(t==null)continue;const s=t instanceof ArrayBuffer?new Uint8Array(t):typeof t=="string"?Zt(t):t;let r=new Uint8Array(e.length+s.length);r.set(e),r.set(s,e.length),e=r;let a;for(;(a=ia(e))!==-1;)yield e.slice(0,a),e=e.slice(a)}e.length>0&&(yield e)}class la{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;const a={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],a}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,r]=ua(e,":");return r.startsWith(" ")&&(r=r.substring(1)),t==="event"?this.event=r:t==="data"&&this.data.push(r),null}}function ua(n,e){const t=n.indexOf(e);return t!==-1?[n.substring(0,t),e,n.substring(t+e.length)]:[n,"",""]}async function Rn(n,e){const{response:t,requestLogID:s,retryOfRequestLogID:r,startTime:a}=e,i=await(async()=>{var f;if(e.options.stream)return H(n).debug("response",t.status,t.url,t.headers,t.body),e.options.__streamClass?e.options.__streamClass.fromSSEResponse(t,e.controller):ne.fromSSEResponse(t,e.controller);if(t.status===204)return null;if(e.options.__binaryResponse)return t;const o=t.headers.get("content-type"),l=(f=o==null?void 0:o.split(";")[0])==null?void 0:f.trim();if((l==null?void 0:l.includes("application/json"))||(l==null?void 0:l.endsWith("+json"))){const h=await t.json();return $n(h,t)}return await t.text()})();return H(n).debug(`[${s}] response parsed`,ce({retryOfRequestLogID:r,url:t.url,status:t.status,body:i,durationMs:Date.now()-a})),i}function $n(n,e){return!n||typeof n!="object"||Array.isArray(n)?n:Object.defineProperty(n,"_request_id",{value:e.headers.get("x-request-id"),enumerable:!1})}var Re;class ut extends Promise{constructor(e,t,s=Rn){super(r=>{r(null)}),this.responsePromise=t,this.parseResponse=s,Re.set(this,void 0),k(this,Re,e)}_thenUnwrap(e){return new ut(c(this,Re,"f"),this.responsePromise,async(t,s)=>$n(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){const[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(c(this,Re,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}Re=new WeakMap;var De;class En{constructor(e,t,s,r){De.set(this,void 0),k(this,De,e),this.options=r,this.response=t,this.body=s}hasNextPage(){return this.getPaginatedItems().length?this.nextPageRequestOptions()!=null:!1}async getNextPage(){const e=this.nextPageRequestOptions();if(!e)throw new O("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await c(this,De,"f").requestAPIList(this.constructor,e)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(De=new WeakMap,Symbol.asyncIterator)](){for await(const e of this.iterPages())for(const t of e.getPaginatedItems())yield t}}class da extends ut{constructor(e,t,s){super(e,t,async(r,a)=>new s(r,a.response,await Rn(r,a),a.options))}async*[Symbol.asyncIterator](){const e=await this;for await(const t of e)yield t}}class dt extends En{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class q extends En{constructor(e,t,s,r){super(e,t,s,r),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageRequestOptions(){var s;const e=this.getPaginatedItems(),t=(s=e[e.length-1])==null?void 0:s.id;return t?{...this.options,query:{...Mr(this.options.query),after:t}}:null}}const Cn=()=>{var n;if(typeof File>"u"){const{process:e}=globalThis,t=typeof((n=e==null?void 0:e.versions)==null?void 0:n.node)=="string"&&parseInt(e.versions.node.split("."))<20;throw new Error("`File` is not defined as a global, which is required for file uploads."+(t?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function Pe(n,e,t){return Cn(),new File(n,e??"unknown_file",t)}function Ge(n){return(typeof n=="object"&&n!==null&&("name"in n&&n.name&&String(n.name)||"url"in n&&n.url&&String(n.url)||"filename"in n&&n.filename&&String(n.filename)||"path"in n&&n.path&&String(n.path))||"").split(/[\\/]/).pop()||void 0}const In=n=>n!=null&&typeof n=="object"&&typeof n[Symbol.asyncIterator]=="function",he=async(n,e)=>({...n,body:await fa(n.body,e)}),Is=new WeakMap;function ha(n){const e=typeof n=="function"?n:n.fetch,t=Is.get(e);if(t)return t;const s=(async()=>{try{const r="Response"in e?e.Response:(await e("data:,")).constructor,a=new FormData;return a.toString()!==await new r(a).text()}catch{return!0}})();return Is.set(e,s),s}const fa=async(n,e)=>{if(!await ha(e))throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");const t=new FormData;return await Promise.all(Object.entries(n||{}).map(([s,r])=>Jt(t,s,r))),t},ma=n=>n instanceof Blob&&"name"in n,Jt=async(n,e,t)=>{if(t!==void 0){if(t==null)throw new TypeError(`Received null for "${e}"; to pass null in FormData, you must use the string 'null'`);if(typeof t=="string"||typeof t=="number"||typeof t=="boolean")n.append(e,String(t));else if(t instanceof Response)n.append(e,Pe([await t.blob()],Ge(t)));else if(In(t))n.append(e,Pe([await new Response(bn(t)).blob()],Ge(t)));else if(ma(t))n.append(e,t,Ge(t));else if(Array.isArray(t))await Promise.all(t.map(s=>Jt(n,e+"[]",s)));else if(typeof t=="object")await Promise.all(Object.entries(t).map(([s,r])=>Jt(n,`${e}[${s}]`,r)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${t} instead`)}},On=n=>n!=null&&typeof n=="object"&&typeof n.size=="number"&&typeof n.type=="string"&&typeof n.text=="function"&&typeof n.slice=="function"&&typeof n.arrayBuffer=="function",pa=n=>n!=null&&typeof n=="object"&&typeof n.name=="string"&&typeof n.lastModified=="number"&&On(n),ga=n=>n!=null&&typeof n=="object"&&typeof n.url=="string"&&typeof n.blob=="function";async function _a(n,e,t){if(Cn(),n=await n,pa(n))return n instanceof File?n:Pe([await n.arrayBuffer()],n.name);if(ga(n)){const r=await n.blob();return e||(e=new URL(n.url).pathname.split(/[\\/]/).pop()),Pe(await Ht(r),e,t)}const s=await Ht(n);if(e||(e=Ge(n)),!(t!=null&&t.type)){const r=s.find(a=>typeof a=="object"&&"type"in a&&a.type);typeof r=="string"&&(t={...t,type:r})}return Pe(s,e,t)}async function Ht(n){var t;let e=[];if(typeof n=="string"||ArrayBuffer.isView(n)||n instanceof ArrayBuffer)e.push(n);else if(On(n))e.push(n instanceof Blob?n:await n.arrayBuffer());else if(In(n))for await(const s of n)e.push(...await Ht(s));else{const s=(t=n==null?void 0:n.constructor)==null?void 0:t.name;throw new Error(`Unexpected data type: ${typeof n}${s?`; constructor: ${s}`:""}${wa(n)}`)}return e}function wa(n){return typeof n!="object"||n===null?"":`; props: [${Object.getOwnPropertyNames(n).map(t=>`"${t}"`).join(", ")}]`}class P{constructor(e){this._client=e}}function kn(n){return n.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}const ya=(n=kn)=>function(t,...s){if(t.length===1)return t[0];let r=!1;const a=t.reduce((y,f,h)=>(/[?#]/.test(f)&&(r=!0),y+f+(h===s.length?"":(r?encodeURIComponent:n)(String(s[h])))),""),i=a.split(/[?#]/,1)[0],o=[],l=new RegExp("(?<=^|\\/)(?:\\.|%2e){1,2}(?=\\/|$)","gi");let m;for(;(m=l.exec(i))!==null;)o.push({start:m.index,length:m[0].length});if(o.length>0){let y=0;const f=o.reduce((h,u)=>{const g=" ".repeat(u.start-y),S="^".repeat(u.length);return y=u.start+u.length,h+g+S},"");throw new O(`Path parameters result in path with invalid segments:
${a}
${f}`)}return a},b=ya(kn);let Pn=class extends P{list(e,t={},s){return this._client.getAPIList(b`/chat/completions/${e}/messages`,q,{query:t,...s})}};function ba(n){return typeof n.parse=="function"}const st=n=>(n==null?void 0:n.role)==="assistant",Tn=n=>(n==null?void 0:n.role)==="tool";var Xt,Ve,Ke,$e,Ee,Qe,Ce,ae,Ie,nt,rt,_e,Mn;class es{constructor(){Xt.add(this),this.controller=new AbortController,Ve.set(this,void 0),Ke.set(this,()=>{}),$e.set(this,()=>{}),Ee.set(this,void 0),Qe.set(this,()=>{}),Ce.set(this,()=>{}),ae.set(this,{}),Ie.set(this,!1),nt.set(this,!1),rt.set(this,!1),_e.set(this,!1),k(this,Ve,new Promise((e,t)=>{k(this,Ke,e,"f"),k(this,$e,t,"f")})),k(this,Ee,new Promise((e,t)=>{k(this,Qe,e,"f"),k(this,Ce,t,"f")})),c(this,Ve,"f").catch(()=>{}),c(this,Ee,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},c(this,Xt,"m",Mn).bind(this))},0)}_connected(){this.ended||(c(this,Ke,"f").call(this),this._emit("connect"))}get ended(){return c(this,Ie,"f")}get errored(){return c(this,nt,"f")}get aborted(){return c(this,rt,"f")}abort(){this.controller.abort()}on(e,t){return(c(this,ae,"f")[e]||(c(this,ae,"f")[e]=[])).push({listener:t}),this}off(e,t){const s=c(this,ae,"f")[e];if(!s)return this;const r=s.findIndex(a=>a.listener===t);return r>=0&&s.splice(r,1),this}once(e,t){return(c(this,ae,"f")[e]||(c(this,ae,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{k(this,_e,!0),e!=="error"&&this.once("error",s),this.once(e,t)})}async done(){k(this,_e,!0),await c(this,Ee,"f")}_emit(e,...t){if(c(this,Ie,"f"))return;e==="end"&&(k(this,Ie,!0),c(this,Qe,"f").call(this));const s=c(this,ae,"f")[e];if(s&&(c(this,ae,"f")[e]=s.filter(r=>!r.once),s.forEach(({listener:r})=>r(...t))),e==="abort"){const r=t[0];!c(this,_e,"f")&&!(s!=null&&s.length)&&Promise.reject(r),c(this,$e,"f").call(this,r),c(this,Ce,"f").call(this,r),this._emit("end");return}if(e==="error"){const r=t[0];!c(this,_e,"f")&&!(s!=null&&s.length)&&Promise.reject(r),c(this,$e,"f").call(this,r),c(this,Ce,"f").call(this,r),this._emit("end")}}_emitFinal(){}}Ve=new WeakMap,Ke=new WeakMap,$e=new WeakMap,Ee=new WeakMap,Qe=new WeakMap,Ce=new WeakMap,ae=new WeakMap,Ie=new WeakMap,nt=new WeakMap,rt=new WeakMap,_e=new WeakMap,Xt=new WeakSet,Mn=function(e){if(k(this,nt,!0),e instanceof Error&&e.name==="AbortError"&&(e=new Q),e instanceof Q)return k(this,rt,!0),this._emit("abort",e);if(e instanceof O)return this._emit("error",e);if(e instanceof Error){const t=new O(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new O(String(e)))};function ts(n){return(n==null?void 0:n.$brand)==="auto-parseable-response-format"}function qe(n){return(n==null?void 0:n.$brand)==="auto-parseable-tool"}function Sa(n,e){return!e||!Nn(e)?{...n,choices:n.choices.map(t=>({...t,message:{...t.message,parsed:null,...t.message.tool_calls?{tool_calls:t.message.tool_calls}:void 0}}))}:ss(n,e)}function ss(n,e){const t=n.choices.map(s=>{var r;if(s.finish_reason==="length")throw new _n;if(s.finish_reason==="content_filter")throw new wn;return{...s,message:{...s.message,...s.message.tool_calls?{tool_calls:((r=s.message.tool_calls)==null?void 0:r.map(a=>Aa(e,a)))??void 0}:void 0,parsed:s.message.content&&!s.message.refusal?xa(e,s.message.content):null}}});return{...n,choices:t}}function xa(n,e){var t,s;return((t=n.response_format)==null?void 0:t.type)!=="json_schema"?null:((s=n.response_format)==null?void 0:s.type)==="json_schema"?"$parseRaw"in n.response_format?n.response_format.$parseRaw(e):JSON.parse(e):null}function Aa(n,e){var s;const t=(s=n.tools)==null?void 0:s.find(r=>{var a;return((a=r.function)==null?void 0:a.name)===e.function.name});return{...e,function:{...e.function,parsed_arguments:qe(t)?t.$parseRaw(e.function.arguments):t!=null&&t.function.strict?JSON.parse(e.function.arguments):null}}}function va(n,e){var s;if(!n)return!1;const t=(s=n.tools)==null?void 0:s.find(r=>{var a;return((a=r.function)==null?void 0:a.name)===e.function.name});return qe(t)||(t==null?void 0:t.function.strict)||!1}function Nn(n){var e;return ts(n.response_format)?!0:((e=n.tools)==null?void 0:e.some(t=>qe(t)||t.type==="function"&&t.function.strict===!0))??!1}function Ra(n){for(const e of n??[]){if(e.type!=="function")throw new O(`Currently only \`function\` tool types support auto-parsing; Received \`${e.type}\``);if(e.function.strict!==!0)throw new O(`The \`${e.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}var X,Gt,at,Vt,Kt,Qt,Fn,qn;const $a=10;class Ln extends es{constructor(){super(...arguments),X.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){var s;this._chatCompletions.push(e),this._emit("chatCompletion",e);const t=(s=e.choices[0])==null?void 0:s.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),Tn(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(st(e)&&e.tool_calls)for(const s of e.tool_calls)s.type==="function"&&this._emit("functionToolCall",s.function)}}async finalChatCompletion(){await this.done();const e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new O("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),c(this,X,"m",Gt).call(this)}async finalMessage(){return await this.done(),c(this,X,"m",at).call(this)}async finalFunctionToolCall(){return await this.done(),c(this,X,"m",Vt).call(this)}async finalFunctionToolCallResult(){return await this.done(),c(this,X,"m",Kt).call(this)}async totalUsage(){return await this.done(),c(this,X,"m",Qt).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){const e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);const t=c(this,X,"m",at).call(this);t&&this._emit("finalMessage",t);const s=c(this,X,"m",Gt).call(this);s&&this._emit("finalContent",s);const r=c(this,X,"m",Vt).call(this);r&&this._emit("finalFunctionToolCall",r);const a=c(this,X,"m",Kt).call(this);a!=null&&this._emit("finalFunctionToolCallResult",a),this._chatCompletions.some(i=>i.usage)&&this._emit("totalUsage",c(this,X,"m",Qt).call(this))}async _createChatCompletion(e,t,s){const r=s==null?void 0:s.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),c(this,X,"m",Fn).call(this,t);const a=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(ss(a,t))}async _runChatCompletion(e,t,s){for(const r of t.messages)this._addMessage(r,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){var u,g,S;const r="tool",{tool_choice:a="auto",stream:i,...o}=t,l=typeof a!="string"&&((u=a==null?void 0:a.function)==null?void 0:u.name),{maxChatCompletions:m=$a}=s||{},y=t.tools.map(d=>{if(qe(d)){if(!d.$callback)throw new O("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:d.$callback,name:d.function.name,description:d.function.description||"",parameters:d.function.parameters,parse:d.$parseRaw,strict:!0}}}return d}),f={};for(const d of y)d.type==="function"&&(f[d.function.name||d.function.function.name]=d.function);const h="tools"in t?y.map(d=>d.type==="function"?{type:"function",function:{name:d.function.name||d.function.function.name,parameters:d.function.parameters,description:d.function.description,strict:d.function.strict}}:d):void 0;for(const d of t.messages)this._addMessage(d,!1);for(let d=0;d<m;++d){const p=(g=(await this._createChatCompletion(e,{...o,tool_choice:a,tools:h,messages:[...this.messages]},s)).choices[0])==null?void 0:g.message;if(!p)throw new O("missing message in ChatCompletion response");if(!((S=p.tool_calls)!=null&&S.length))return;for(const _ of p.tool_calls){if(_.type!=="function")continue;const w=_.id,{name:T,arguments:$}=_.function,v=f[T];if(v){if(l&&l!==T){const R=`Invalid tool_call: ${JSON.stringify(T)}. ${JSON.stringify(l)} requested. Please try again`;this._addMessage({role:r,tool_call_id:w,content:R});continue}}else{const R=`Invalid tool_call: ${JSON.stringify(T)}. Available options are: ${Object.keys(f).map(M=>JSON.stringify(M)).join(", ")}. Please try again`;this._addMessage({role:r,tool_call_id:w,content:R});continue}let I;try{I=ba(v)?await v.parse($):$}catch(R){const M=R instanceof Error?R.message:String(R);this._addMessage({role:r,tool_call_id:w,content:M});continue}const x=await v.function(I,this),E=c(this,X,"m",qn).call(this,x);if(this._addMessage({role:r,tool_call_id:w,content:E}),l)return}}}}X=new WeakSet,Gt=function(){return c(this,X,"m",at).call(this).content??null},at=function(){let e=this.messages.length;for(;e-- >0;){const t=this.messages[e];if(st(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new O("stream ended without producing a ChatCompletionMessage with role=assistant")},Vt=function(){var e,t;for(let s=this.messages.length-1;s>=0;s--){const r=this.messages[s];if(st(r)&&((e=r==null?void 0:r.tool_calls)!=null&&e.length))return(t=r.tool_calls.at(-1))==null?void 0:t.function}},Kt=function(){for(let e=this.messages.length-1;e>=0;e--){const t=this.messages[e];if(Tn(t)&&t.content!=null&&typeof t.content=="string"&&this.messages.some(s=>{var r;return s.role==="assistant"&&((r=s.tool_calls)==null?void 0:r.some(a=>a.type==="function"&&a.id===t.tool_call_id))}))return t.content}},Qt=function(){const e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(const{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},Fn=function(e){if(e.n!=null&&e.n>1)throw new O("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},qn=function(e){return typeof e=="string"?e:e===void 0?"undefined":JSON.stringify(e)};class ns extends Ln{static runTools(e,t,s){const r=new ns,a={...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,a)),r}_addMessage(e,t=!0){super._addMessage(e,t),st(e)&&e.content&&this._emit("content",e.content)}}const Bn=1,jn=2,Dn=4,Un=8,Wn=16,Jn=32,Hn=64,Xn=128,Gn=256,Vn=Xn|Gn,Kn=Wn|Jn|Vn|Hn,Qn=Bn|jn|Kn,zn=Dn|Un,Ea=Qn|zn,U={STR:Bn,NUM:jn,ARR:Dn,OBJ:Un,NULL:Wn,BOOL:Jn,NAN:Hn,INFINITY:Xn,MINUS_INFINITY:Gn,INF:Vn,SPECIAL:Kn,ATOM:Qn,COLLECTION:zn,ALL:Ea};class Ca extends Error{}class Ia extends Error{}function Oa(n,e=U.ALL){if(typeof n!="string")throw new TypeError(`expecting str, got ${typeof n}`);if(!n.trim())throw new Error(`${n} is empty`);return ka(n.trim(),e)}const ka=(n,e)=>{const t=n.length;let s=0;const r=h=>{throw new Ca(`${h} at position ${s}`)},a=h=>{throw new Ia(`${h} at position ${s}`)},i=()=>(f(),s>=t&&r("Unexpected end of input"),n[s]==='"'?o():n[s]==="{"?l():n[s]==="["?m():n.substring(s,s+4)==="null"||U.NULL&e&&t-s<4&&"null".startsWith(n.substring(s))?(s+=4,null):n.substring(s,s+4)==="true"||U.BOOL&e&&t-s<4&&"true".startsWith(n.substring(s))?(s+=4,!0):n.substring(s,s+5)==="false"||U.BOOL&e&&t-s<5&&"false".startsWith(n.substring(s))?(s+=5,!1):n.substring(s,s+8)==="Infinity"||U.INFINITY&e&&t-s<8&&"Infinity".startsWith(n.substring(s))?(s+=8,1/0):n.substring(s,s+9)==="-Infinity"||U.MINUS_INFINITY&e&&1<t-s&&t-s<9&&"-Infinity".startsWith(n.substring(s))?(s+=9,-1/0):n.substring(s,s+3)==="NaN"||U.NAN&e&&t-s<3&&"NaN".startsWith(n.substring(s))?(s+=3,NaN):y()),o=()=>{const h=s;let u=!1;for(s++;s<t&&(n[s]!=='"'||u&&n[s-1]==="\\");)u=n[s]==="\\"?!u:!1,s++;if(n.charAt(s)=='"')try{return JSON.parse(n.substring(h,++s-Number(u)))}catch(g){a(String(g))}else if(U.STR&e)try{return JSON.parse(n.substring(h,s-Number(u))+'"')}catch{return JSON.parse(n.substring(h,n.lastIndexOf("\\"))+'"')}r("Unterminated string literal")},l=()=>{s++,f();const h={};try{for(;n[s]!=="}";){if(f(),s>=t&&U.OBJ&e)return h;const u=o();f(),s++;try{const g=i();Object.defineProperty(h,u,{value:g,writable:!0,enumerable:!0,configurable:!0})}catch(g){if(U.OBJ&e)return h;throw g}f(),n[s]===","&&s++}}catch{if(U.OBJ&e)return h;r("Expected '}' at end of object")}return s++,h},m=()=>{s++;const h=[];try{for(;n[s]!=="]";)h.push(i()),f(),n[s]===","&&s++}catch{if(U.ARR&e)return h;r("Expected ']' at end of array")}return s++,h},y=()=>{if(s===0){n==="-"&&U.NUM&e&&r("Not sure what '-' is");try{return JSON.parse(n)}catch(u){if(U.NUM&e)try{return n[n.length-1]==="."?JSON.parse(n.substring(0,n.lastIndexOf("."))):JSON.parse(n.substring(0,n.lastIndexOf("e")))}catch{}a(String(u))}}const h=s;for(n[s]==="-"&&s++;n[s]&&!",]}".includes(n[s]);)s++;s==t&&!(U.NUM&e)&&r("Unterminated number literal");try{return JSON.parse(n.substring(h,s))}catch{n.substring(h,s)==="-"&&U.NUM&e&&r("Not sure what '-' is");try{return JSON.parse(n.substring(h,n.lastIndexOf("e")))}catch(g){a(String(g))}}},f=()=>{for(;s<t&&` 
\r	`.includes(n[s]);)s++};return i()},Os=n=>Oa(n,U.ALL^U.NUM);var B,re,me,ie,yt,Ue,bt,St,xt,We,At,ks;class Ne extends Ln{constructor(e){super(),B.add(this),re.set(this,void 0),me.set(this,void 0),ie.set(this,void 0),k(this,re,e),k(this,me,[])}get currentChatCompletionSnapshot(){return c(this,ie,"f")}static fromReadableStream(e){const t=new Ne(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){const r=new Ne(t);return r._run(()=>r._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createChatCompletion(e,t,s){var i;super._createChatCompletion;const r=s==null?void 0:s.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),c(this,B,"m",yt).call(this);const a=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});this._connected();for await(const o of a)c(this,B,"m",bt).call(this,o);if((i=a.controller.signal)!=null&&i.aborted)throw new Q;return this._addChatCompletion(c(this,B,"m",We).call(this))}async _fromReadableStream(e,t){var i;const s=t==null?void 0:t.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),c(this,B,"m",yt).call(this),this._connected();const r=ne.fromReadableStream(e,this.controller);let a;for await(const o of r)a&&a!==o.id&&this._addChatCompletion(c(this,B,"m",We).call(this)),c(this,B,"m",bt).call(this,o),a=o.id;if((i=r.controller.signal)!=null&&i.aborted)throw new Q;return this._addChatCompletion(c(this,B,"m",We).call(this))}[(re=new WeakMap,me=new WeakMap,ie=new WeakMap,B=new WeakSet,yt=function(){this.ended||k(this,ie,void 0)},Ue=function(t){let s=c(this,me,"f")[t.index];return s||(s={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},c(this,me,"f")[t.index]=s,s)},bt=function(t){var r,a,i,o,l,m,y,f,h,u,g,S,d,A,p;if(this.ended)return;const s=c(this,B,"m",ks).call(this,t);this._emit("chunk",t,s);for(const _ of t.choices){const w=s.choices[_.index];_.delta.content!=null&&((r=w.message)==null?void 0:r.role)==="assistant"&&((a=w.message)!=null&&a.content)&&(this._emit("content",_.delta.content,w.message.content),this._emit("content.delta",{delta:_.delta.content,snapshot:w.message.content,parsed:w.message.parsed})),_.delta.refusal!=null&&((i=w.message)==null?void 0:i.role)==="assistant"&&((o=w.message)!=null&&o.refusal)&&this._emit("refusal.delta",{delta:_.delta.refusal,snapshot:w.message.refusal}),((l=_.logprobs)==null?void 0:l.content)!=null&&((m=w.message)==null?void 0:m.role)==="assistant"&&this._emit("logprobs.content.delta",{content:(y=_.logprobs)==null?void 0:y.content,snapshot:((f=w.logprobs)==null?void 0:f.content)??[]}),((h=_.logprobs)==null?void 0:h.refusal)!=null&&((u=w.message)==null?void 0:u.role)==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:(g=_.logprobs)==null?void 0:g.refusal,snapshot:((S=w.logprobs)==null?void 0:S.refusal)??[]});const T=c(this,B,"m",Ue).call(this,w);w.finish_reason&&(c(this,B,"m",xt).call(this,w),T.current_tool_call_index!=null&&c(this,B,"m",St).call(this,w,T.current_tool_call_index));for(const $ of _.delta.tool_calls??[])T.current_tool_call_index!==$.index&&(c(this,B,"m",xt).call(this,w),T.current_tool_call_index!=null&&c(this,B,"m",St).call(this,w,T.current_tool_call_index)),T.current_tool_call_index=$.index;for(const $ of _.delta.tool_calls??[]){const v=(d=w.message.tool_calls)==null?void 0:d[$.index];v!=null&&v.type&&((v==null?void 0:v.type)==="function"?this._emit("tool_calls.function.arguments.delta",{name:(A=v.function)==null?void 0:A.name,index:$.index,arguments:v.function.arguments,parsed_arguments:v.function.parsed_arguments,arguments_delta:((p=$.function)==null?void 0:p.arguments)??""}):(v==null||v.type,void 0))}}},St=function(t,s){var i,o,l;if(c(this,B,"m",Ue).call(this,t).done_tool_calls.has(s))return;const a=(i=t.message.tool_calls)==null?void 0:i[s];if(!a)throw new Error("no tool call snapshot");if(!a.type)throw new Error("tool call snapshot missing `type`");if(a.type==="function"){const m=(l=(o=c(this,re,"f"))==null?void 0:o.tools)==null?void 0:l.find(y=>y.type==="function"&&y.function.name===a.function.name);this._emit("tool_calls.function.arguments.done",{name:a.function.name,index:s,arguments:a.function.arguments,parsed_arguments:qe(m)?m.$parseRaw(a.function.arguments):m!=null&&m.function.strict?JSON.parse(a.function.arguments):null})}else a.type},xt=function(t){var r,a;const s=c(this,B,"m",Ue).call(this,t);if(t.message.content&&!s.content_done){s.content_done=!0;const i=c(this,B,"m",At).call(this);this._emit("content.done",{content:t.message.content,parsed:i?i.$parseRaw(t.message.content):null})}t.message.refusal&&!s.refusal_done&&(s.refusal_done=!0,this._emit("refusal.done",{refusal:t.message.refusal})),(r=t.logprobs)!=null&&r.content&&!s.logprobs_content_done&&(s.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:t.logprobs.content})),(a=t.logprobs)!=null&&a.refusal&&!s.logprobs_refusal_done&&(s.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:t.logprobs.refusal}))},We=function(){if(this.ended)throw new O("stream has ended, this shouldn't happen");const t=c(this,ie,"f");if(!t)throw new O("request ended without sending any chunks");return k(this,ie,void 0),k(this,me,[]),Pa(t,c(this,re,"f"))},At=function(){var s;const t=(s=c(this,re,"f"))==null?void 0:s.response_format;return ts(t)?t:null},ks=function(t){var s,r,a,i;let o=c(this,ie,"f");const{choices:l,...m}=t;o?Object.assign(o,m):o=k(this,ie,{...m,choices:[]});for(const{delta:y,finish_reason:f,index:h,logprobs:u=null,...g}of t.choices){let S=o.choices[h];if(S||(S=o.choices[h]={finish_reason:f,index:h,message:{},logprobs:u,...g}),u)if(!S.logprobs)S.logprobs=Object.assign({},u);else{const{content:$,refusal:v,...I}=u;Object.assign(S.logprobs,I),$&&((s=S.logprobs).content??(s.content=[]),S.logprobs.content.push(...$)),v&&((r=S.logprobs).refusal??(r.refusal=[]),S.logprobs.refusal.push(...v))}if(f&&(S.finish_reason=f,c(this,re,"f")&&Nn(c(this,re,"f")))){if(f==="length")throw new _n;if(f==="content_filter")throw new wn}if(Object.assign(S,g),!y)continue;const{content:d,refusal:A,function_call:p,role:_,tool_calls:w,...T}=y;if(Object.assign(S.message,T),A&&(S.message.refusal=(S.message.refusal||"")+A),_&&(S.message.role=_),p&&(S.message.function_call?(p.name&&(S.message.function_call.name=p.name),p.arguments&&((a=S.message.function_call).arguments??(a.arguments=""),S.message.function_call.arguments+=p.arguments)):S.message.function_call=p),d&&(S.message.content=(S.message.content||"")+d,!S.message.refusal&&c(this,B,"m",At).call(this)&&(S.message.parsed=Os(S.message.content))),w){S.message.tool_calls||(S.message.tool_calls=[]);for(const{index:$,id:v,type:I,function:x,...E}of w){const R=(i=S.message.tool_calls)[$]??(i[$]={});Object.assign(R,E),v&&(R.id=v),I&&(R.type=I),x&&(R.function??(R.function={name:x.name??"",arguments:""})),x!=null&&x.name&&(R.function.name=x.name),x!=null&&x.arguments&&(R.function.arguments+=x.arguments,va(c(this,re,"f"),R)&&(R.function.parsed_arguments=Os(R.function.arguments)))}}}return o},Symbol.asyncIterator)](){const e=[],t=[];let s=!1;return this.on("chunk",r=>{const a=t.shift();a?a.resolve(r):e.push(r)}),this.on("end",()=>{s=!0;for(const r of t)r.resolve(void 0);t.length=0}),this.on("abort",r=>{s=!0;for(const a of t)a.reject(r);t.length=0}),this.on("error",r=>{s=!0;for(const a of t)a.reject(r);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((a,i)=>t.push({resolve:a,reject:i})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new ne(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function Pa(n,e){const{id:t,choices:s,created:r,model:a,system_fingerprint:i,...o}=n,l={...o,id:t,choices:s.map(({message:m,finish_reason:y,index:f,logprobs:h,...u})=>{if(!y)throw new O(`missing finish_reason for choice ${f}`);const{content:g=null,function_call:S,tool_calls:d,...A}=m,p=m.role;if(!p)throw new O(`missing role for choice ${f}`);if(S){const{arguments:_,name:w}=S;if(_==null)throw new O(`missing function_call.arguments for choice ${f}`);if(!w)throw new O(`missing function_call.name for choice ${f}`);return{...u,message:{content:g,function_call:{arguments:_,name:w},role:p,refusal:m.refusal??null},finish_reason:y,index:f,logprobs:h}}return d?{...u,index:f,finish_reason:y,logprobs:h,message:{...A,role:p,content:g,refusal:m.refusal??null,tool_calls:d.map((_,w)=>{const{function:T,type:$,id:v,...I}=_,{arguments:x,name:E,...R}=T||{};if(v==null)throw new O(`missing choices[${f}].tool_calls[${w}].id
${Je(n)}`);if($==null)throw new O(`missing choices[${f}].tool_calls[${w}].type
${Je(n)}`);if(E==null)throw new O(`missing choices[${f}].tool_calls[${w}].function.name
${Je(n)}`);if(x==null)throw new O(`missing choices[${f}].tool_calls[${w}].function.arguments
${Je(n)}`);return{...I,id:v,type:$,function:{...R,name:E,arguments:x}}})}}:{...u,message:{...A,content:g,role:p,refusal:m.refusal??null},finish_reason:y,index:f,logprobs:h}}),created:r,model:a,object:"chat.completion",...i?{system_fingerprint:i}:{}};return Sa(l,e)}function Je(n){return JSON.stringify(n)}class it extends Ne{static fromReadableStream(e){const t=new it(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){const r=new it(t),a={...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"runTools"}};return r._run(()=>r._runTools(e,t,a)),r}}let rs=class extends P{constructor(){super(...arguments),this.messages=new Pn(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(b`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(b`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",q,{query:e,...t})}delete(e,t){return this._client.delete(b`/chat/completions/${e}`,t)}parse(e,t){return Ra(e.tools),this._client.chat.completions.create(e,{...t,headers:{...t==null?void 0:t.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(s=>ss(s,e))}runTools(e,t){return e.stream?it.runTools(this._client,e,t):ns.runTools(this._client,e,t)}stream(e,t){return Ne.createChatCompletion(this._client,e,t)}};rs.Messages=Pn;class as extends P{constructor(){super(...arguments),this.completions=new rs(this._client)}}as.Completions=rs;const Yn=Symbol("brand.privateNullableHeaders"),Ps=Array.isArray;function*Ta(n){if(!n)return;if(Yn in n){const{values:s,nulls:r}=n;yield*s.entries();for(const a of r)yield[a,null];return}let e=!1,t;n instanceof Headers?t=n.entries():Ps(n)?t=n:(e=!0,t=Object.entries(n??{}));for(let s of t){const r=s[0];if(typeof r!="string")throw new TypeError("expected header name to be a string");const a=Ps(s[1])?s[1]:[s[1]];let i=!1;for(const o of a)o!==void 0&&(e&&!i&&(i=!0,yield[r,null]),yield[r,o])}}const C=n=>{const e=new Headers,t=new Set;for(const s of n){const r=new Set;for(const[a,i]of Ta(s)){const o=a.toLowerCase();r.has(o)||(e.delete(a),r.add(o)),i===null?(e.delete(a),t.add(o)):(e.append(a,i),t.delete(o))}}return{[Yn]:!0,values:e,nulls:t}};class Zn extends P{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:C([{Accept:"application/octet-stream"},t==null?void 0:t.headers]),__binaryResponse:!0})}}class er extends P{create(e,t){return this._client.post("/audio/transcriptions",he({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class tr extends P{create(e,t){return this._client.post("/audio/translations",he({body:e,...t,__metadata:{model:e.model}},this._client))}}class Le extends P{constructor(){super(...arguments),this.transcriptions=new er(this._client),this.translations=new tr(this._client),this.speech=new Zn(this._client)}}Le.Transcriptions=er;Le.Translations=tr;Le.Speech=Zn;class sr extends P{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(b`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",q,{query:e,...t})}cancel(e,t){return this._client.post(b`/batches/${e}/cancel`,t)}}class nr extends P{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}retrieve(e,t){return this._client.get(b`/assistants/${e}`,{...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}update(e,t,s){return this._client.post(b`/assistants/${e}`,{body:t,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e={},t){return this._client.getAPIList("/assistants",q,{query:e,...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}delete(e,t){return this._client.delete(b`/assistants/${e}`,{...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}}class rr extends P{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}}class ar extends P{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}}class ht extends P{constructor(){super(...arguments),this.sessions=new rr(this._client),this.transcriptionSessions=new ar(this._client)}}ht.Sessions=rr;ht.TranscriptionSessions=ar;class ir extends P{create(e,t,s){return this._client.post(b`/threads/${e}/messages`,{body:t,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}retrieve(e,t,s){const{thread_id:r}=t;return this._client.get(b`/threads/${r}/messages/${e}`,{...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}update(e,t,s){const{thread_id:r,...a}=t;return this._client.post(b`/threads/${r}/messages/${e}`,{body:a,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e,t={},s){return this._client.getAPIList(b`/threads/${e}/messages`,q,{query:t,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}delete(e,t,s){const{thread_id:r}=t;return this._client.delete(b`/threads/${r}/messages/${e}`,{...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}class or extends P{retrieve(e,t,s){const{thread_id:r,run_id:a,...i}=t;return this._client.get(b`/threads/${r}/runs/${a}/steps/${e}`,{query:i,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e,t,s){const{thread_id:r,...a}=t;return this._client.getAPIList(b`/threads/${r}/runs/${e}/steps`,q,{query:a,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}const Ma=n=>{if(typeof Buffer<"u"){const e=Buffer.from(n,"base64");return Array.from(new Float32Array(e.buffer,e.byteOffset,e.length/Float32Array.BYTES_PER_ELEMENT))}else{const e=atob(n),t=e.length,s=new Uint8Array(t);for(let r=0;r<t;r++)s[r]=e.charCodeAt(r);return Array.from(new Float32Array(s.buffer))}};var vt={};const xe=n=>{var e,t,s,r;if(typeof globalThis.process<"u")return((e=vt==null?void 0:vt[n])==null?void 0:e.trim())??void 0;if(typeof globalThis.Deno<"u")return(r=(s=(t=globalThis.Deno.env)==null?void 0:t.get)==null?void 0:s.call(t,n))==null?void 0:r.trim()};var W,ue,zt,se,ze,z,de,we,le,ot,K,Ye,Ze,Te,Oe,ke,Ts,Ms,Ns,Fs,qs,Ls,Bs;class Me extends es{constructor(){super(...arguments),W.add(this),zt.set(this,[]),se.set(this,{}),ze.set(this,{}),z.set(this,void 0),de.set(this,void 0),we.set(this,void 0),le.set(this,void 0),ot.set(this,void 0),K.set(this,void 0),Ye.set(this,void 0),Ze.set(this,void 0),Te.set(this,void 0)}[(zt=new WeakMap,se=new WeakMap,ze=new WeakMap,z=new WeakMap,de=new WeakMap,we=new WeakMap,le=new WeakMap,ot=new WeakMap,K=new WeakMap,Ye=new WeakMap,Ze=new WeakMap,Te=new WeakMap,W=new WeakSet,Symbol.asyncIterator)](){const e=[],t=[];let s=!1;return this.on("event",r=>{const a=t.shift();a?a.resolve(r):e.push(r)}),this.on("end",()=>{s=!0;for(const r of t)r.resolve(void 0);t.length=0}),this.on("abort",r=>{s=!0;for(const a of t)a.reject(r);t.length=0}),this.on("error",r=>{s=!0;for(const a of t)a.reject(r);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((a,i)=>t.push({resolve:a,reject:i})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){const t=new ue;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){var a;const s=t==null?void 0:t.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();const r=ne.fromReadableStream(e,this.controller);for await(const i of r)c(this,W,"m",Oe).call(this,i);if((a=r.controller.signal)!=null&&a.aborted)throw new Q;return this._addRun(c(this,W,"m",ke).call(this))}toReadableStream(){return new ne(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,r){const a=new ue;return a._run(()=>a._runToolAssistantStream(e,t,s,{...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"stream"}})),a}async _createToolAssistantStream(e,t,s,r){var l;const a=r==null?void 0:r.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));const i={...s,stream:!0},o=await e.submitToolOutputs(t,i,{...r,signal:this.controller.signal});this._connected();for await(const m of o)c(this,W,"m",Oe).call(this,m);if((l=o.controller.signal)!=null&&l.aborted)throw new Q;return this._addRun(c(this,W,"m",ke).call(this))}static createThreadAssistantStream(e,t,s){const r=new ue;return r._run(()=>r._threadAssistantStream(e,t,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),r}static createAssistantStream(e,t,s,r){const a=new ue;return a._run(()=>a._runAssistantStream(e,t,s,{...r,headers:{...r==null?void 0:r.headers,"X-Stainless-Helper-Method":"stream"}})),a}currentEvent(){return c(this,Ye,"f")}currentRun(){return c(this,Ze,"f")}currentMessageSnapshot(){return c(this,z,"f")}currentRunStepSnapshot(){return c(this,Te,"f")}async finalRunSteps(){return await this.done(),Object.values(c(this,se,"f"))}async finalMessages(){return await this.done(),Object.values(c(this,ze,"f"))}async finalRun(){if(await this.done(),!c(this,de,"f"))throw Error("Final run was not received.");return c(this,de,"f")}async _createThreadAssistantStream(e,t,s){var o;const r=s==null?void 0:s.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));const a={...t,stream:!0},i=await e.createAndRun(a,{...s,signal:this.controller.signal});this._connected();for await(const l of i)c(this,W,"m",Oe).call(this,l);if((o=i.controller.signal)!=null&&o.aborted)throw new Q;return this._addRun(c(this,W,"m",ke).call(this))}async _createAssistantStream(e,t,s,r){var l;const a=r==null?void 0:r.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));const i={...s,stream:!0},o=await e.create(t,i,{...r,signal:this.controller.signal});this._connected();for await(const m of o)c(this,W,"m",Oe).call(this,m);if((l=o.controller.signal)!=null&&l.aborted)throw new Q;return this._addRun(c(this,W,"m",ke).call(this))}static accumulateDelta(e,t){for(const[s,r]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=r;continue}let a=e[s];if(a==null){e[s]=r;continue}if(s==="index"||s==="type"){e[s]=r;continue}if(typeof a=="string"&&typeof r=="string")a+=r;else if(typeof a=="number"&&typeof r=="number")a+=r;else if(gt(a)&&gt(r))a=this.accumulateDelta(a,r);else if(Array.isArray(a)&&Array.isArray(r)){if(a.every(i=>typeof i=="string"||typeof i=="number")){a.push(...r);continue}for(const i of r){if(!gt(i))throw new Error(`Expected array delta entry to be an object but got: ${i}`);const o=i.index;if(o==null)throw console.error(i),new Error("Expected array delta entry to have an `index` property");if(typeof o!="number")throw new Error(`Expected array delta entry \`index\` property to be a number but got ${o}`);const l=a[o];l==null?a.push(i):a[o]=this.accumulateDelta(l,i)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${r}, accValue: ${a}`);e[s]=a}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,r){return await this._createAssistantStream(t,e,s,r)}async _runToolAssistantStream(e,t,s,r){return await this._createToolAssistantStream(t,e,s,r)}}ue=Me,Oe=function(e){if(!this.ended)switch(k(this,Ye,e),c(this,W,"m",Ns).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":c(this,W,"m",Bs).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":c(this,W,"m",Ms).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":c(this,W,"m",Ts).call(this,e);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},ke=function(){if(this.ended)throw new O("stream has ended, this shouldn't happen");if(!c(this,de,"f"))throw Error("Final run has not been received");return c(this,de,"f")},Ts=function(e){const[t,s]=c(this,W,"m",qs).call(this,e,c(this,z,"f"));k(this,z,t),c(this,ze,"f")[t.id]=t;for(const r of s){const a=t.content[r.index];(a==null?void 0:a.type)=="text"&&this._emit("textCreated",a.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(const r of e.data.delta.content){if(r.type=="text"&&r.text){let a=r.text,i=t.content[r.index];if(i&&i.type=="text")this._emit("textDelta",a,i.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(r.index!=c(this,we,"f")){if(c(this,le,"f"))switch(c(this,le,"f").type){case"text":this._emit("textDone",c(this,le,"f").text,c(this,z,"f"));break;case"image_file":this._emit("imageFileDone",c(this,le,"f").image_file,c(this,z,"f"));break}k(this,we,r.index)}k(this,le,t.content[r.index])}break;case"thread.message.completed":case"thread.message.incomplete":if(c(this,we,"f")!==void 0){const r=e.data.content[c(this,we,"f")];if(r)switch(r.type){case"image_file":this._emit("imageFileDone",r.image_file,c(this,z,"f"));break;case"text":this._emit("textDone",r.text,c(this,z,"f"));break}}c(this,z,"f")&&this._emit("messageDone",e.data),k(this,z,void 0)}},Ms=function(e){const t=c(this,W,"m",Fs).call(this,e);switch(k(this,Te,t),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":const s=e.data.delta;if(s.step_details&&s.step_details.type=="tool_calls"&&s.step_details.tool_calls&&t.step_details.type=="tool_calls")for(const a of s.step_details.tool_calls)a.index==c(this,ot,"f")?this._emit("toolCallDelta",a,t.step_details.tool_calls[a.index]):(c(this,K,"f")&&this._emit("toolCallDone",c(this,K,"f")),k(this,ot,a.index),k(this,K,t.step_details.tool_calls[a.index]),c(this,K,"f")&&this._emit("toolCallCreated",c(this,K,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":k(this,Te,void 0),e.data.step_details.type=="tool_calls"&&c(this,K,"f")&&(this._emit("toolCallDone",c(this,K,"f")),k(this,K,void 0)),this._emit("runStepDone",e.data,t);break}},Ns=function(e){c(this,zt,"f").push(e),this._emit("event",e)},Fs=function(e){switch(e.event){case"thread.run.step.created":return c(this,se,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=c(this,se,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){const r=ue.accumulateDelta(t,s.delta);c(this,se,"f")[e.data.id]=r}return c(this,se,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":c(this,se,"f")[e.data.id]=e.data;break}if(c(this,se,"f")[e.data.id])return c(this,se,"f")[e.data.id];throw new Error("No snapshot available")},qs=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let r=e.data;if(r.delta.content)for(const a of r.delta.content)if(a.index in t.content){let i=t.content[a.index];t.content[a.index]=c(this,W,"m",Ls).call(this,a,i)}else t.content[a.index]=a,s.push(a);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},Ls=function(e,t){return ue.accumulateDelta(t,e)},Bs=function(e){switch(k(this,Ze,e.data),e.event){case"thread.run.created":break;case"thread.run.queued":break;case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":k(this,de,e.data),c(this,K,"f")&&(this._emit("toolCallDone",c(this,K,"f")),k(this,K,void 0));break}};let is=class extends P{constructor(){super(...arguments),this.steps=new or(this._client)}create(e,t,s){const{include:r,...a}=t;return this._client.post(b`/threads/${e}/runs`,{query:{include:r},body:a,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers]),stream:t.stream??!1})}retrieve(e,t,s){const{thread_id:r}=t;return this._client.get(b`/threads/${r}/runs/${e}`,{...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}update(e,t,s){const{thread_id:r,...a}=t;return this._client.post(b`/threads/${r}/runs/${e}`,{body:a,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e,t={},s){return this._client.getAPIList(b`/threads/${e}/runs`,q,{query:t,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}cancel(e,t,s){const{thread_id:r}=t;return this._client.post(b`/threads/${r}/runs/${e}/cancel`,{...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async createAndPoll(e,t,s){const r=await this.create(e,t,s);return await this.poll(r.id,{thread_id:e},s)}createAndStream(e,t,s){return Me.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){var a;const r=C([s==null?void 0:s.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((a=s==null?void 0:s.pollIntervalMs)==null?void 0:a.toString())??void 0}]);for(;;){const{data:i,response:o}=await this.retrieve(e,t,{...s,headers:{...s==null?void 0:s.headers,...r}}).withResponse();switch(i.status){case"queued":case"in_progress":case"cancelling":let l=5e3;if(s!=null&&s.pollIntervalMs)l=s.pollIntervalMs;else{const m=o.headers.get("openai-poll-after-ms");if(m){const y=parseInt(m);isNaN(y)||(l=y)}}await Fe(l);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return i}}}stream(e,t,s){return Me.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){const{thread_id:r,...a}=t;return this._client.post(b`/threads/${r}/runs/${e}/submit_tool_outputs`,{body:a,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){const r=await this.submitToolOutputs(e,t,s);return await this.poll(r.id,t,s)}submitToolOutputsStream(e,t,s){return Me.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}};is.Steps=or;class ft extends P{constructor(){super(...arguments),this.runs=new is(this._client),this.messages=new ir(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}retrieve(e,t){return this._client.get(b`/threads/${e}`,{...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}update(e,t,s){return this._client.post(b`/threads/${e}`,{body:t,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}delete(e,t){return this._client.delete(b`/threads/${e}`,{...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){const s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return Me.createThreadAssistantStream(e,this._client.beta.threads,t)}}ft.Runs=is;ft.Messages=ir;class Be extends P{constructor(){super(...arguments),this.realtime=new ht(this._client),this.assistants=new nr(this._client),this.threads=new ft(this._client)}}Be.Realtime=ht;Be.Assistants=nr;Be.Threads=ft;class cr extends P{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class lr extends P{retrieve(e,t,s){const{container_id:r}=t;return this._client.get(b`/containers/${r}/files/${e}/content`,{...s,headers:C([{Accept:"application/binary"},s==null?void 0:s.headers]),__binaryResponse:!0})}}let os=class extends P{constructor(){super(...arguments),this.content=new lr(this._client)}create(e,t,s){return this._client.post(b`/containers/${e}/files`,he({body:t,...s},this._client))}retrieve(e,t,s){const{container_id:r}=t;return this._client.get(b`/containers/${r}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(b`/containers/${e}/files`,q,{query:t,...s})}delete(e,t,s){const{container_id:r}=t;return this._client.delete(b`/containers/${r}/files/${e}`,{...s,headers:C([{Accept:"*/*"},s==null?void 0:s.headers])})}};os.Content=lr;class cs extends P{constructor(){super(...arguments),this.files=new os(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(b`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",q,{query:e,...t})}delete(e,t){return this._client.delete(b`/containers/${e}`,{...t,headers:C([{Accept:"*/*"},t==null?void 0:t.headers])})}}cs.Files=os;class ur extends P{create(e,t){const s=!!e.encoding_format;let r=s?e.encoding_format:"base64";s&&H(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);const a=this._client.post("/embeddings",{body:{...e,encoding_format:r},...t});return s?a:(H(this._client).debug("embeddings/decoding base64 embeddings from base64"),a._thenUnwrap(i=>(i&&i.data&&i.data.forEach(o=>{const l=o.embedding;o.embedding=Ma(l)}),i)))}}class dr extends P{retrieve(e,t,s){const{eval_id:r,run_id:a}=t;return this._client.get(b`/evals/${r}/runs/${a}/output_items/${e}`,s)}list(e,t,s){const{eval_id:r,...a}=t;return this._client.getAPIList(b`/evals/${r}/runs/${e}/output_items`,q,{query:a,...s})}}class ls extends P{constructor(){super(...arguments),this.outputItems=new dr(this._client)}create(e,t,s){return this._client.post(b`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){const{eval_id:r}=t;return this._client.get(b`/evals/${r}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(b`/evals/${e}/runs`,q,{query:t,...s})}delete(e,t,s){const{eval_id:r}=t;return this._client.delete(b`/evals/${r}/runs/${e}`,s)}cancel(e,t,s){const{eval_id:r}=t;return this._client.post(b`/evals/${r}/runs/${e}`,s)}}ls.OutputItems=dr;class us extends P{constructor(){super(...arguments),this.runs=new ls(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(b`/evals/${e}`,t)}update(e,t,s){return this._client.post(b`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",q,{query:e,...t})}delete(e,t){return this._client.delete(b`/evals/${e}`,t)}}us.Runs=ls;let hr=class extends P{create(e,t){return this._client.post("/files",he({body:e,...t},this._client))}retrieve(e,t){return this._client.get(b`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",q,{query:e,...t})}delete(e,t){return this._client.delete(b`/files/${e}`,t)}content(e,t){return this._client.get(b`/files/${e}/content`,{...t,headers:C([{Accept:"application/binary"},t==null?void 0:t.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=30*60*1e3}={}){const r=new Set(["processed","error","deleted"]),a=Date.now();let i=await this.retrieve(e);for(;!i.status||!r.has(i.status);)if(await Fe(t),i=await this.retrieve(e),Date.now()-a>s)throw new Yt({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return i}};class fr extends P{}let mr=class extends P{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}};class ds extends P{constructor(){super(...arguments),this.graders=new mr(this._client)}}ds.Graders=mr;class pr extends P{create(e,t,s){return this._client.getAPIList(b`/fine_tuning/checkpoints/${e}/permissions`,dt,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.get(b`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}delete(e,t,s){const{fine_tuned_model_checkpoint:r}=t;return this._client.delete(b`/fine_tuning/checkpoints/${r}/permissions/${e}`,s)}}let hs=class extends P{constructor(){super(...arguments),this.permissions=new pr(this._client)}};hs.Permissions=pr;class gr extends P{list(e,t={},s){return this._client.getAPIList(b`/fine_tuning/jobs/${e}/checkpoints`,q,{query:t,...s})}}class fs extends P{constructor(){super(...arguments),this.checkpoints=new gr(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(b`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",q,{query:e,...t})}cancel(e,t){return this._client.post(b`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(b`/fine_tuning/jobs/${e}/events`,q,{query:t,...s})}pause(e,t){return this._client.post(b`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(b`/fine_tuning/jobs/${e}/resume`,t)}}fs.Checkpoints=gr;class ye extends P{constructor(){super(...arguments),this.methods=new fr(this._client),this.jobs=new fs(this._client),this.checkpoints=new hs(this._client),this.alpha=new ds(this._client)}}ye.Methods=fr;ye.Jobs=fs;ye.Checkpoints=hs;ye.Alpha=ds;class _r extends P{}class ms extends P{constructor(){super(...arguments),this.graderModels=new _r(this._client)}}ms.GraderModels=_r;class wr extends P{createVariation(e,t){return this._client.post("/images/variations",he({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",he({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class yr extends P{retrieve(e,t){return this._client.get(b`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",dt,e)}delete(e,t){return this._client.delete(b`/models/${e}`,t)}}class br extends P{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function Na(n,e){return!e||!qa(e)?{...n,output_parsed:null,output:n.output.map(t=>t.type==="function_call"?{...t,parsed_arguments:null}:t.type==="message"?{...t,content:t.content.map(s=>({...s,parsed:null}))}:t)}:Sr(n,e)}function Sr(n,e){const t=n.output.map(r=>{if(r.type==="function_call")return{...r,parsed_arguments:ja(e,r)};if(r.type==="message"){const a=r.content.map(i=>i.type==="output_text"?{...i,parsed:Fa(e,i.text)}:i);return{...r,content:a}}return r}),s=Object.assign({},n,{output:t});return Object.getOwnPropertyDescriptor(n,"output_text")||xr(s),Object.defineProperty(s,"output_parsed",{enumerable:!0,get(){for(const r of s.output)if(r.type==="message"){for(const a of r.content)if(a.type==="output_text"&&a.parsed!==null)return a.parsed}return null}}),s}function Fa(n,e){var t,s,r,a;return((s=(t=n.text)==null?void 0:t.format)==null?void 0:s.type)!=="json_schema"?null:"$parseRaw"in((r=n.text)==null?void 0:r.format)?((a=n.text)==null?void 0:a.format).$parseRaw(e):JSON.parse(e)}function qa(n){var e;return!!ts((e=n.text)==null?void 0:e.format)}function La(n){return(n==null?void 0:n.$brand)==="auto-parseable-tool"}function Ba(n,e){return n.find(t=>t.type==="function"&&t.name===e)}function ja(n,e){const t=Ba(n.tools??[],e.name);return{...e,...e,parsed_arguments:La(t)?t.$parseRaw(e.arguments):t!=null&&t.strict?JSON.parse(e.arguments):null}}function xr(n){const e=[];for(const t of n.output)if(t.type==="message")for(const s of t.content)s.type==="output_text"&&e.push(s.text);n.output_text=e.join("")}var pe,He,oe,Xe,js,Ds,Us,Ws;class ps extends es{constructor(e){super(),pe.add(this),He.set(this,void 0),oe.set(this,void 0),Xe.set(this,void 0),k(this,He,e)}static createResponse(e,t,s){const r=new ps(t);return r._run(()=>r._createOrRetrieveResponse(e,t,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createOrRetrieveResponse(e,t,s){var o;const r=s==null?void 0:s.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),c(this,pe,"m",js).call(this);let a,i=null;"response_id"in t?(a=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):a=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected();for await(const l of a)c(this,pe,"m",Ds).call(this,l,i);if((o=a.controller.signal)!=null&&o.aborted)throw new Q;return c(this,pe,"m",Us).call(this)}[(He=new WeakMap,oe=new WeakMap,Xe=new WeakMap,pe=new WeakSet,js=function(){this.ended||k(this,oe,void 0)},Ds=function(t,s){if(this.ended)return;const r=(i,o)=>{(s==null||o.sequence_number>s)&&this._emit(i,o)},a=c(this,pe,"m",Ws).call(this,t);switch(r("event",t),t.type){case"response.output_text.delta":{const i=a.output[t.output_index];if(!i)throw new O(`missing output at index ${t.output_index}`);if(i.type==="message"){const o=i.content[t.content_index];if(!o)throw new O(`missing content at index ${t.content_index}`);if(o.type!=="output_text")throw new O(`expected content to be 'output_text', got ${o.type}`);r("response.output_text.delta",{...t,snapshot:o.text})}break}case"response.function_call_arguments.delta":{const i=a.output[t.output_index];if(!i)throw new O(`missing output at index ${t.output_index}`);i.type==="function_call"&&r("response.function_call_arguments.delta",{...t,snapshot:i.arguments});break}default:r(t.type,t);break}},Us=function(){if(this.ended)throw new O("stream has ended, this shouldn't happen");const t=c(this,oe,"f");if(!t)throw new O("request ended without sending any events");k(this,oe,void 0);const s=Da(t,c(this,He,"f"));return k(this,Xe,s),s},Ws=function(t){let s=c(this,oe,"f");if(!s){if(t.type!=="response.created")throw new O(`When snapshot hasn't been set yet, expected 'response.created' event, got ${t.type}`);return s=k(this,oe,t.response),s}switch(t.type){case"response.output_item.added":{s.output.push(t.item);break}case"response.content_part.added":{const r=s.output[t.output_index];if(!r)throw new O(`missing output at index ${t.output_index}`);r.type==="message"&&r.content.push(t.part);break}case"response.output_text.delta":{const r=s.output[t.output_index];if(!r)throw new O(`missing output at index ${t.output_index}`);if(r.type==="message"){const a=r.content[t.content_index];if(!a)throw new O(`missing content at index ${t.content_index}`);if(a.type!=="output_text")throw new O(`expected content to be 'output_text', got ${a.type}`);a.text+=t.delta}break}case"response.function_call_arguments.delta":{const r=s.output[t.output_index];if(!r)throw new O(`missing output at index ${t.output_index}`);r.type==="function_call"&&(r.arguments+=t.delta);break}case"response.completed":{k(this,oe,t.response);break}}return s},Symbol.asyncIterator)](){const e=[],t=[];let s=!1;return this.on("event",r=>{const a=t.shift();a?a.resolve(r):e.push(r)}),this.on("end",()=>{s=!0;for(const r of t)r.resolve(void 0);t.length=0}),this.on("abort",r=>{s=!0;for(const a of t)a.reject(r);t.length=0}),this.on("error",r=>{s=!0;for(const a of t)a.reject(r);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((a,i)=>t.push({resolve:a,reject:i})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();const e=c(this,Xe,"f");if(!e)throw new O("stream ended without producing a ChatCompletion");return e}}function Da(n,e){return Na(n,e)}class Ar extends P{list(e,t={},s){return this._client.getAPIList(b`/responses/${e}/input_items`,q,{query:t,...s})}}class gs extends P{constructor(){super(...arguments),this.inputItems=new Ar(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(s=>("object"in s&&s.object==="response"&&xr(s),s))}retrieve(e,t={},s){return this._client.get(b`/responses/${e}`,{query:t,...s,stream:(t==null?void 0:t.stream)??!1})}delete(e,t){return this._client.delete(b`/responses/${e}`,{...t,headers:C([{Accept:"*/*"},t==null?void 0:t.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(s=>Sr(s,e))}stream(e,t){return ps.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(b`/responses/${e}/cancel`,t)}}gs.InputItems=Ar;class vr extends P{create(e,t,s){return this._client.post(b`/uploads/${e}/parts`,he({body:t,...s},this._client))}}class _s extends P{constructor(){super(...arguments),this.parts=new vr(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(b`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(b`/uploads/${e}/complete`,{body:t,...s})}}_s.Parts=vr;const Ua=async n=>{const e=await Promise.allSettled(n),t=e.filter(r=>r.status==="rejected");if(t.length){for(const r of t)console.error(r.reason);throw new Error(`${t.length} promise(s) failed - see the above errors`)}const s=[];for(const r of e)r.status==="fulfilled"&&s.push(r.value);return s};class Rr extends P{create(e,t,s){return this._client.post(b`/vector_stores/${e}/file_batches`,{body:t,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}retrieve(e,t,s){const{vector_store_id:r}=t;return this._client.get(b`/vector_stores/${r}/file_batches/${e}`,{...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}cancel(e,t,s){const{vector_store_id:r}=t;return this._client.post(b`/vector_stores/${r}/file_batches/${e}/cancel`,{...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async createAndPoll(e,t,s){const r=await this.create(e,t);return await this.poll(e,r.id,s)}listFiles(e,t,s){const{vector_store_id:r,...a}=t;return this._client.getAPIList(b`/vector_stores/${r}/file_batches/${e}/files`,q,{query:a,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async poll(e,t,s){var a;const r=C([s==null?void 0:s.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((a=s==null?void 0:s.pollIntervalMs)==null?void 0:a.toString())??void 0}]);for(;;){const{data:i,response:o}=await this.retrieve(t,{vector_store_id:e},{...s,headers:r}).withResponse();switch(i.status){case"in_progress":let l=5e3;if(s!=null&&s.pollIntervalMs)l=s.pollIntervalMs;else{const m=o.headers.get("openai-poll-after-ms");if(m){const y=parseInt(m);isNaN(y)||(l=y)}}await Fe(l);break;case"failed":case"cancelled":case"completed":return i}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},r){if(t==null||t.length==0)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");const a=(r==null?void 0:r.maxConcurrency)??5,i=Math.min(a,t.length),o=this._client,l=t.values(),m=[...s];async function y(h){for(let u of h){const g=await o.files.create({file:u,purpose:"assistants"},r);m.push(g.id)}}const f=Array(i).fill(l).map(y);return await Ua(f),await this.createAndPoll(e,{file_ids:m})}}class $r extends P{create(e,t,s){return this._client.post(b`/vector_stores/${e}/files`,{body:t,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}retrieve(e,t,s){const{vector_store_id:r}=t;return this._client.get(b`/vector_stores/${r}/files/${e}`,{...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}update(e,t,s){const{vector_store_id:r,...a}=t;return this._client.post(b`/vector_stores/${r}/files/${e}`,{body:a,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e,t={},s){return this._client.getAPIList(b`/vector_stores/${e}/files`,q,{query:t,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}delete(e,t,s){const{vector_store_id:r}=t;return this._client.delete(b`/vector_stores/${r}/files/${e}`,{...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async createAndPoll(e,t,s){const r=await this.create(e,t,s);return await this.poll(e,r.id,s)}async poll(e,t,s){var a;const r=C([s==null?void 0:s.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((a=s==null?void 0:s.pollIntervalMs)==null?void 0:a.toString())??void 0}]);for(;;){const i=await this.retrieve(t,{vector_store_id:e},{...s,headers:r}).withResponse(),o=i.data;switch(o.status){case"in_progress":let l=5e3;if(s!=null&&s.pollIntervalMs)l=s.pollIntervalMs;else{const m=i.response.headers.get("openai-poll-after-ms");if(m){const y=parseInt(m);isNaN(y)||(l=y)}}await Fe(l);break;case"failed":case"completed":return o}}}async upload(e,t,s){const r=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:r.id},s)}async uploadAndPoll(e,t,s){const r=await this.upload(e,t,s);return await this.poll(e,r.id,s)}content(e,t,s){const{vector_store_id:r}=t;return this._client.getAPIList(b`/vector_stores/${r}/files/${e}/content`,dt,{...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}class mt extends P{constructor(){super(...arguments),this.files=new $r(this._client),this.fileBatches=new Rr(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}retrieve(e,t){return this._client.get(b`/vector_stores/${e}`,{...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}update(e,t,s){return this._client.post(b`/vector_stores/${e}`,{body:t,...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",q,{query:e,...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}delete(e,t){return this._client.delete(b`/vector_stores/${e}`,{...t,headers:C([{"OpenAI-Beta":"assistants=v2"},t==null?void 0:t.headers])})}search(e,t,s){return this._client.getAPIList(b`/vector_stores/${e}/search`,dt,{body:t,method:"post",...s,headers:C([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}mt.Files=$r;mt.FileBatches=Rr;var ws,et;class N{constructor({baseURL:e=xe("OPENAI_BASE_URL"),apiKey:t=xe("OPENAI_API_KEY"),organization:s=xe("OPENAI_ORG_ID")??null,project:r=xe("OPENAI_PROJECT_ID")??null,...a}={}){if(et.set(this,void 0),this.completions=new cr(this),this.chat=new as(this),this.embeddings=new ur(this),this.files=new hr(this),this.images=new wr(this),this.audio=new Le(this),this.moderations=new br(this),this.models=new yr(this),this.fineTuning=new ye(this),this.graders=new ms(this),this.vectorStores=new mt(this),this.beta=new Be(this),this.batches=new sr(this),this.uploads=new _s(this),this.responses=new gs(this),this.evals=new us(this),this.containers=new cs(this),t===void 0)throw new O("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");const i={apiKey:t,organization:s,project:r,...a,baseURL:e||"https://api.openai.com/v1"};if(!i.dangerouslyAllowBrowser&&jr())throw new O(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new OpenAI({ apiKey, dangerouslyAllowBrowser: true });

https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety
`);this.baseURL=i.baseURL,this.timeout=i.timeout??ws.DEFAULT_TIMEOUT,this.logger=i.logger??console;const o="warn";this.logLevel=o,this.logLevel=bs(i.logLevel,"ClientOptions.logLevel",this)??bs(xe("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??o,this.fetchOptions=i.fetchOptions,this.maxRetries=i.maxRetries??2,this.fetch=i.fetch??Hr(),k(this,et,Gr),this._options=i,this.apiKey=t,this.organization=s,this.project=r}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return C([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return na(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${ge}`}defaultIdempotencyKey(){return`stainless-node-retry-${cn()}`}makeStatusError(e,t,s,r){return J.generate(e,t,s,r)}buildURL(e,t){const s=Tr(e)?new URL(e):new URL(this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),r=this.defaultQuery();return Nr(r)||(t={...r,...t}),typeof t=="object"&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(r=>({method:e,path:t,...r})))}request(e,t=null){return new ut(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){var A,p;const r=await e,a=r.maxRetries??this.maxRetries;t==null&&(t=a),await this.prepareOptions(r);const{req:i,url:o,timeout:l}=this.buildRequest(r,{retryCount:a-t});await this.prepareRequest(i,{url:o,options:r});const m="log_"+(Math.random()*(1<<24)|0).toString(16).padStart(6,"0"),y=s===void 0?"":`, retryOf: ${s}`,f=Date.now();if(H(this).debug(`[${m}] sending request`,ce({retryOfRequestLogID:s,method:r.method,url:o,options:r,headers:i.headers})),(A=r.signal)!=null&&A.aborted)throw new Q;const h=new AbortController,u=await this.fetchWithTimeout(o,i,l,h).catch(Dt),g=Date.now();if(u instanceof Error){const _=`retrying, ${t} attempts remaining`;if((p=r.signal)!=null&&p.aborted)throw new Q;const w=jt(u)||/timed? ?out/i.test(String(u)+("cause"in u?String(u.cause):""));if(t)return H(this).info(`[${m}] connection ${w?"timed out":"failed"} - ${_}`),H(this).debug(`[${m}] connection ${w?"timed out":"failed"} (${_})`,ce({retryOfRequestLogID:s,url:o,durationMs:g-f,message:u.message})),this.retryRequest(r,t,s??m);throw H(this).info(`[${m}] connection ${w?"timed out":"failed"} - error; no more retries left`),H(this).debug(`[${m}] connection ${w?"timed out":"failed"} (error; no more retries left)`,ce({retryOfRequestLogID:s,url:o,durationMs:g-f,message:u.message})),w?new Yt:new ct({cause:u})}const S=[...u.headers.entries()].filter(([_])=>_==="x-request-id").map(([_,w])=>", "+_+": "+JSON.stringify(w)).join(""),d=`[${m}${y}${S}] ${i.method} ${o} ${u.ok?"succeeded":"failed"} with status ${u.status} in ${g-f}ms`;if(!u.ok){const _=this.shouldRetry(u);if(t&&_){const x=`retrying, ${t} attempts remaining`;return await Xr(u.body),H(this).info(`${d} - ${x}`),H(this).debug(`[${m}] response error (${x})`,ce({retryOfRequestLogID:s,url:u.url,status:u.status,headers:u.headers,durationMs:g-f})),this.retryRequest(r,t,s??m,u.headers)}const w=_?"error; no more retries left":"error; not retryable";H(this).info(`${d} - ${w}`);const T=await u.text().catch(x=>Dt(x).message),$=Lr(T),v=$?void 0:T;throw H(this).debug(`[${m}] response error (${w})`,ce({retryOfRequestLogID:s,url:u.url,status:u.status,headers:u.headers,message:v,durationMs:Date.now()-f})),this.makeStatusError(u.status,$,v,u.headers)}return H(this).info(d),H(this).debug(`[${m}] response start`,ce({retryOfRequestLogID:s,url:u.url,status:u.status,headers:u.headers,durationMs:g-f})),{response:u,options:r,controller:h,requestLogID:m,retryOfRequestLogID:s,startTime:f}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){const s=this.makeRequest(t,null,void 0);return new da(this,s,e)}async fetchWithTimeout(e,t,s,r){const{signal:a,method:i,...o}=t||{};a&&a.addEventListener("abort",()=>r.abort());const l=setTimeout(()=>r.abort(),s),m=globalThis.ReadableStream&&o.body instanceof globalThis.ReadableStream||typeof o.body=="object"&&o.body!==null&&Symbol.asyncIterator in o.body,y={signal:r.signal,...m?{duplex:"half"}:{},method:"GET",...o};i&&(y.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,y)}finally{clearTimeout(l)}}shouldRetry(e){const t=e.headers.get("x-should-retry");return t==="true"?!0:t==="false"?!1:e.status===408||e.status===409||e.status===429||e.status>=500}async retryRequest(e,t,s,r){let a;const i=r==null?void 0:r.get("retry-after-ms");if(i){const l=parseFloat(i);Number.isNaN(l)||(a=l)}const o=r==null?void 0:r.get("retry-after");if(o&&!a){const l=parseFloat(o);Number.isNaN(l)?a=Date.parse(o)-Date.now():a=l*1e3}if(!(a&&0<=a&&a<60*1e3)){const l=e.maxRetries??this.maxRetries;a=this.calculateDefaultRetryTimeoutMillis(t,l)}return await Fe(a),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){const a=t-e,i=Math.min(.5*Math.pow(2,a),8),o=1-Math.random()*.25;return i*o*1e3}buildRequest(e,{retryCount:t=0}={}){const s={...e},{method:r,path:a,query:i}=s,o=this.buildURL(a,i);"timeout"in s&&qr("timeout",s.timeout),s.timeout=s.timeout??this.timeout;const{bodyHeaders:l,body:m}=this.buildBody({options:s}),y=this.buildHeaders({options:e,method:r,bodyHeaders:l,retryCount:t});return{req:{method:r,headers:y,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&m instanceof globalThis.ReadableStream&&{duplex:"half"},...m&&{body:m},...this.fetchOptions??{},...s.fetchOptions??{}},url:o,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:r}){let a={};this.idempotencyHeader&&t!=="get"&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),a[this.idempotencyHeader]=e.idempotencyKey);const i=C([a,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(r),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...Jr(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};const s=C([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||typeof e=="string"&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:typeof e=="object"&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&typeof e.next=="function")?{bodyHeaders:void 0,body:bn(e)}:c(this,et,"f").call(this,{body:e,headers:s})}}ws=N,et=new WeakMap;N.OpenAI=ws;N.DEFAULT_TIMEOUT=6e5;N.OpenAIError=O;N.APIError=J;N.APIConnectionError=ct;N.APIConnectionTimeoutError=Yt;N.APIUserAbortError=Q;N.NotFoundError=hn;N.ConflictError=fn;N.RateLimitError=pn;N.BadRequestError=ln;N.AuthenticationError=un;N.InternalServerError=gn;N.PermissionDeniedError=dn;N.UnprocessableEntityError=mn;N.toFile=_a;N.Completions=cr;N.Chat=as;N.Embeddings=ur;N.Files=hr;N.Images=wr;N.Audio=Le;N.Moderations=br;N.Models=yr;N.FineTuning=ye;N.Graders=ms;N.VectorStores=mt;N.Beta=Be;N.Batches=sr;N.Uploads=_s;N.Responses=gs;N.Evals=us;N.Containers=cs;var Ae={exports:{}},Rt,Js;function ys(){if(Js)return Rt;Js=1;class n extends Error{constructor(t,s,r){super(t),this.name="ApiError",this.request=s,this.response=r}}return Rt=n,Rt}var $t,Hs;function Wa(){if(Hs)return $t;Hs=1;class n{constructor(t,s,r=null){this.owner=t,this.name=s,this.version=r}static parse(t){const s=t.match(/^(?<owner>[^/]+)\/(?<name>[^/:]+)(:(?<version>.+))?$/);if(!s)throw new Error(`Invalid reference to model version: ${t}. Expected format: owner/name or owner/name:version`);const{owner:r,name:a,version:i}=s.groups;return new n(r,a,i)}}return $t=n,$t}function Xs(n){throw new Error('Could not dynamically require "'+n+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Et,Gs;function Er(){if(Gs)return Et;Gs=1;async function n(r,a={}){const i=new FormData;let o,l;if(r instanceof Blob)o=r.name||`blob_${Date.now()}`,l=r;else if(Buffer.isBuffer(r)){o=`buffer_${Date.now()}`;const y=new Uint8Array(r);l=new Blob([y],{type:"application/octet-stream",name:o})}else throw new Error("Invalid file argument, must be a Blob, File or Buffer");return i.append("content",l,o),i.append("metadata",new Blob([JSON.stringify(a)],{type:"application/json"})),(await this.request("/files",{method:"POST",data:i,headers:{"Content-Type":"multipart/form-data"}})).json()}async function e(){return(await this.request("/files",{method:"GET"})).json()}async function t(r){return(await this.request(`/files/${r}`,{method:"GET"})).json()}async function s(r){return(await this.request(`/files/${r}`,{method:"DELETE"})).status===204}return Et={create:n,list:e,get:t,delete:s},Et}var Ct,Vs;function pt(){if(Vs)return Ct;Vs=1;const n=ys(),{create:e}=Er();async function t(d,A){let{id:p,timestamp:_,body:w,signature:T}=d;const $=A||d.secret;if(d&&d.headers&&d.body&&(typeof d.headers.get=="function"?(p=d.headers.get("webhook-id"),_=d.headers.get("webhook-timestamp"),T=d.headers.get("webhook-signature")):(p=d.headers["webhook-id"],_=d.headers["webhook-timestamp"],T=d.headers["webhook-signature"]),w=d.body),w instanceof ReadableStream||w.readable)try{w=await new Response(w).text()}catch(E){throw new Error(`Error reading body: ${E.message}`)}else if(h(w))w=await new Blob([w]).text();else if(typeof w=="object")w=JSON.stringify(w);else if(typeof w!="string")throw new Error("Invalid body type");if(!p||!_||!T)throw new Error("Missing required webhook headers");if(!w)throw new Error("Missing required body");if(!$)throw new Error("Missing required secret");const v=`${p}.${_}.${w}`,I=await s($.split("_").pop(),v);return T.split(" ").map(E=>E.split(",")[1]).some(E=>E===I)}async function s(d,A){const p=new TextEncoder;let _=globalThis.crypto;typeof _>"u"&&typeof Xs=="function"&&(_=Xs.call(null,"node:crypto").webcrypto);const w=await _.subtle.importKey("raw",r(d),{name:"HMAC",hash:"SHA-256"},!1,["sign"]),T=await _.subtle.sign("HMAC",w,p.encode(A));return a(T)}function r(d){return Uint8Array.from(atob(d),A=>A.codePointAt(0))}function a(d){return btoa(String.fromCharCode.apply(null,new Uint8Array(d)))}async function i(d,A={}){const p=A.shouldRetry||(()=>!1),_=A.maxRetries||5,w=A.interval||500,T=A.jitter||100,$=I=>new Promise(x=>setTimeout(x,I));let v=0;do{let I=w*2**v+Math.random()*T;try{const x=await d();if(x.ok||!p(x))return x}catch(x){if(x instanceof n){const E=x.response.headers.get("Retry-After");if(E)if(Number.isInteger(E))I=E*1e3;else{const R=new Date(E);Number.isNaN(R.getTime())||(I=R.getTime()-new Date().getTime())}}}Number.isInteger(_)&&_>0&&(Number.isInteger(I)&&I>0&&await $(w*2**(A.maxRetries-_)),v+=1)}while(v<_);return d()}async function o(d,A,p){switch(p){case"data-uri":return await y(d);case"upload":return await l(d,A);case"default":try{return await l(d,A)}catch(_){if(_ instanceof n&&_.response.status>=400&&_.response.status<500)throw _;return await y(A)}default:throw new Error(`Unexpected file upload strategy: ${p}`)}}async function l(d,A){return await f(A,async p=>p instanceof Blob||p instanceof Buffer?(await e.call(d,p)).urls.get:p)}const m=1e7;async function y(d){let A=0;return await f(d,async p=>{let _,w;if(p instanceof Blob)_=await p.arrayBuffer(),w=p.type;else if(h(p))_=p;else return p;if(A+=_.byteLength,A>m)throw new Error(`Combined filesize of prediction ${A} bytes exceeds 10mb limit for inline encoding, please provide URLs instead`);const T=a(_);return w=w||"application/octet-stream",`data:${w};base64,${T}`})}async function f(d,A){if(Array.isArray(d)){const p=[];for(const _ of d){const w=await f(_,A);p.push(w)}return p}if(u(d)){const p={};for(const _ of Object.keys(d))p[_]=await f(d[_],A);return p}return await A(d)}function h(d){return d instanceof Int8Array||d instanceof Int16Array||d instanceof Int32Array||d instanceof Uint8Array||d instanceof Uint8ClampedArray||d instanceof Uint16Array||d instanceof Uint32Array||d instanceof Float32Array||d instanceof Float64Array}function u(d){if(!(typeof d=="object"&&d!==null)||String(d)!=="[object Object]")return!1;const p=Object.getPrototypeOf(d);if(p===null)return!0;const _=Object.prototype.hasOwnProperty.call(p,"constructor")&&p.constructor;return typeof _=="function"&&_ instanceof _&&Function.prototype.toString.call(_)===Function.prototype.toString.call(Object)}function g(d){const A=typeof d=="object"&&d.logs?d.logs:d;if(!A||typeof A!="string")return null;const p=/^\s*(\d+)%\s*\|.+?\|\s*(\d+)\/(\d+)/,_=A.split(`
`).reverse();for(const w of _){const T=w.match(p);if(T&&T.length===4)return{percentage:parseInt(T[1],10)/100,current:parseInt(T[2],10),total:parseInt(T[3],10)}}return null}async function*S(d){const A=d.getReader();try{for(;;){const{done:p,value:_}=await A.read();if(p)return;yield _}}finally{A.releaseLock()}}return Ct={transform:f,transformFileInputs:o,validateWebhook:t,withAutomaticRetries:i,parseProgressFromLogs:g,streamAsyncIterator:S},Ct}var It,Ks;function Ja(){if(Ks)return It;Ks=1;var n=Object.defineProperty,e=Object.getOwnPropertyDescriptor,t=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,r=(h,u)=>{for(var g in u)n(h,g,{get:u[g],enumerable:!0})},a=(h,u,g,S)=>{if(u&&typeof u=="object"||typeof u=="function")for(let d of t(u))!s.call(h,d)&&d!==g&&n(h,d,{get:()=>u[d],enumerable:!(S=e(u,d))||S.enumerable});return h},i=h=>a(n({},"__esModule",{value:!0}),h),o={};r(o,{EventSourceParserStream:()=>f}),It=i(o);function l(h){let u,g,S,d,A,p,_;return w(),{feed:T,reset:w};function w(){u=!0,g="",S=0,d=-1,A=void 0,p=void 0,_=""}function T(v){g=g?g+v:v,u&&y(g)&&(g=g.slice(m.length)),u=!1;const I=g.length;let x=0,E=!1;for(;x<I;){E&&(g[x]===`
`&&++x,E=!1);let R=-1,M=d,F;for(let L=S;R<0&&L<I;++L)F=g[L],F===":"&&M<0?M=L-x:F==="\r"?(E=!0,R=L-x):F===`
`&&(R=L-x);if(R<0){S=I-x,d=M;break}else S=0,d=-1;$(g,x,M,R),x+=R+1}x===I?g="":x>0&&(g=g.slice(x))}function $(v,I,x,E){if(E===0){_.length>0&&(h({type:"event",id:A,event:p||void 0,data:_.slice(0,-1)}),_="",A=void 0),p=void 0;return}const R=x<0,M=v.slice(I,I+(R?E:x));let F=0;R?F=E:v[I+x+1]===" "?F=x+2:F=x+1;const L=I+F,D=E-F,Y=v.slice(L,L+D).toString();if(M==="data")_+=Y?"".concat(Y,`
`):`
`;else if(M==="event")p=Y;else if(M==="id"&&!Y.includes("\0"))A=Y;else if(M==="retry"){const be=parseInt(Y,10);Number.isNaN(be)||h({type:"reconnect-interval",value:be})}}}var m=[239,187,191];function y(h){return m.every((u,g)=>h.charCodeAt(g)===u)}var f=class extends TransformStream{constructor(){let h;super({start(u){h=l(g=>{g.type==="event"&&u.enqueue(g)})},transform(u){h.feed(u)}})}};return It}var Ot,Qs;function Ha(){if(Qs)return Ot;Qs=1;var n=Object.defineProperty,e=Object.getOwnPropertyDescriptor,t=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,r=(h,u)=>{for(var g in u)n(h,g,{get:u[g],enumerable:!0})},a=(h,u,g,S)=>{if(u&&typeof u=="object"||typeof u=="function")for(let d of t(u))!s.call(h,d)&&d!==g&&n(h,d,{get:()=>u[d],enumerable:!(S=e(u,d))||S.enumerable});return h},i=h=>a(n({},"__esModule",{value:!0}),h),o={};r(o,{TextDecoderStream:()=>f}),Ot=i(o);var l=Symbol("decDecoder"),m=Symbol("decTransform"),y=class{constructor(h){this.decoder_=h}transform(h,u){if(!(h instanceof ArrayBuffer||ArrayBuffer.isView(h)))throw new TypeError("Input data must be a BufferSource");const g=this.decoder_.decode(h,{stream:!0});g.length!==0&&u.enqueue(g)}flush(h){const u=this.decoder_.decode();u.length!==0&&h.enqueue(u)}},f=class{constructor(h,u){const g=new TextDecoder(h||"utf-8",u||{});this[l]=g,this[m]=new TransformStream(new y(g))}get encoding(){return this[l].encoding}get fatal(){return this[l].fatal}get ignoreBOM(){return this[l].ignoreBOM}get readable(){return this[m].readable}get writable(){return this[m].writable}};return Ot}var kt,zs;function Xa(){if(zs)return kt;zs=1;const n=ys(),{streamAsyncIterator:e}=pt(),{EventSourceParserStream:t}=Ja(),{TextDecoderStream:s}=typeof globalThis.TextDecoderStream>"u"?Ha():globalThis;class r{constructor(l,m,y,f){this.event=l,this.data=m,this.id=y,this.retry=f}toString(){return this.event==="output"?this.data:""}}function a({url:o,fetch:l,options:m={}}){const{useFileOutput:y=!0,headers:f={},...h}=m;return new ReadableStream({async start(u){const g={...h,headers:{...f,Accept:"text/event-stream"}},S=await l(o,g);if(!S.ok){const A=await S.text(),p=new Request(o,g);u.error(new n(`Request to ${o} failed with status ${S.status}: ${A}`,p,S))}const d=S.body.pipeThrough(new s).pipeThrough(new t);for await(const A of e(d)){if(A.event==="error"){u.error(new Error(A.data));break}let p=A.data;if(y&&typeof p=="string"&&(p.startsWith("https:")||p.startsWith("data:"))&&(p=i({fetch:l})),u.enqueue(new r(A.event,p,A.id)),A.event==="done")break}u.close()}})}function i({url:o,fetch:l}){let m="application/octet-stream";class y extends ReadableStream{async blob(){const h=[];for await(const u of this)h.push(u);return new Blob(h,{type:m})}url(){return new URL(o)}toString(){return o}}return new y({async start(f){const h=await l(o);if(!h.ok){const u=await h.text(),g=new Request(o,init);f.error(new n(`Request to ${o} failed with status ${h.status}: ${u}`,g,h))}h.headers.get("Content-Type")&&(m=h.headers.get("Content-Type"));try{for await(const u of e(h.body))f.enqueue(u);f.close()}catch(u){f.error(u)}}})}return kt={createFileOutput:i,createReadableStream:a,ServerSentEvent:r},kt}var Pt,Ys;function Ga(){if(Ys)return Pt;Ys=1;async function n(){return(await this.request("/account",{method:"GET"})).json()}return Pt={current:n},Pt}var Tt,Zs;function Va(){if(Zs)return Tt;Zs=1;async function n(t){return(await this.request(`/collections/${t}`,{method:"GET"})).json()}async function e(){return(await this.request("/collections",{method:"GET"})).json()}return Tt={get:n,list:e},Tt}var Mt,en;function Ka(){if(en)return Mt;en=1;const{transformFileInputs:n}=pt();async function e(o,l,m){const{input:y,wait:f,...h}=m;if(h.webhook)try{new URL(h.webhook)}catch{throw new Error("Invalid webhook URL")}const u={};if(f)if(typeof f=="number"){const S=Math.max(1,Math.ceil(Number(f))||1);u.Prefer=`wait=${S}`}else u.Prefer="wait";return(await this.request(`/deployments/${o}/${l}/predictions`,{method:"POST",headers:u,data:{...h,input:await n(this,y,this.fileEncodingStrategy)}})).json()}async function t(o,l){return(await this.request(`/deployments/${o}/${l}`,{method:"GET"})).json()}async function s(o){return(await this.request("/deployments",{method:"POST",data:o})).json()}async function r(o,l,m){return(await this.request(`/deployments/${o}/${l}`,{method:"PATCH",data:m})).json()}async function a(o,l){return(await this.request(`/deployments/${o}/${l}`,{method:"DELETE"})).status===204}async function i(){return(await this.request("/deployments",{method:"GET"})).json()}return Mt={predictions:{create:e},get:t,create:s,update:r,list:i,delete:a},Mt}var Nt,tn;function Qa(){if(tn)return Nt;tn=1;async function n(){return(await this.request("/hardware",{method:"GET"})).json()}return Nt={list:n},Nt}var Ft,sn;function za(){if(sn)return Ft;sn=1;async function n(i,o){return(await this.request(`/models/${i}/${o}`,{method:"GET"})).json()}async function e(i,o){return(await this.request(`/models/${i}/${o}/versions`,{method:"GET"})).json()}async function t(i,o,l){return(await this.request(`/models/${i}/${o}/versions/${l}`,{method:"GET"})).json()}async function s(){return(await this.request("/models",{method:"GET"})).json()}async function r(i,o,l){const m={owner:i,name:o,...l};return(await this.request("/models",{method:"POST",data:m})).json()}async function a(i){return(await this.request("/models",{method:"QUERY",headers:{"Content-Type":"text/plain"},data:i})).json()}return Ft={get:n,list:s,create:r,versions:{list:e,get:t},search:a},Ft}var qt,nn;function Ya(){if(nn)return qt;nn=1;const{transformFileInputs:n}=pt();async function e(a){const{model:i,version:o,input:l,wait:m,...y}=a;if(y.webhook)try{new URL(y.webhook)}catch{throw new Error("Invalid webhook URL")}const f={};if(m)if(typeof m=="number"){const u=Math.max(1,Math.ceil(Number(m))||1);f.Prefer=`wait=${u}`}else f.Prefer="wait";let h;if(o)h=await this.request("/predictions",{method:"POST",headers:f,data:{...y,input:await n(this,l,this.fileEncodingStrategy),version:o}});else if(i)h=await this.request(`/models/${i}/predictions`,{method:"POST",headers:f,data:{...y,input:await n(this,l,this.fileEncodingStrategy)}});else throw new Error("Either model or version must be specified");return h.json()}async function t(a){return(await this.request(`/predictions/${a}`,{method:"GET"})).json()}async function s(a){return(await this.request(`/predictions/${a}/cancel`,{method:"POST"})).json()}async function r(){return(await this.request("/predictions",{method:"GET"})).json()}return qt={create:e,get:t,cancel:s,list:r},qt}var Lt,rn;function Za(){if(rn)return Lt;rn=1;async function n(r,a,i,o){const{...l}=o;if(l.webhook)try{new URL(l.webhook)}catch{throw new Error("Invalid webhook URL")}return(await this.request(`/models/${r}/${a}/versions/${i}/trainings`,{method:"POST",data:l})).json()}async function e(r){return(await this.request(`/trainings/${r}`,{method:"GET"})).json()}async function t(r){return(await this.request(`/trainings/${r}/cancel`,{method:"POST"})).json()}async function s(){return(await this.request("/trainings",{method:"GET"})).json()}return Lt={create:n,get:e,cancel:t,list:s},Lt}var Bt,an;function ei(){if(an)return Bt;an=1;async function n(){return(await this.request("/webhooks/default/secret",{method:"GET"})).json()}return Bt={default:{secret:{get:n}}},Bt}const ti="1.0.1",si={version:ti};var on;function ni(){if(on)return Ae.exports;on=1;var n={};const e=ys(),t=Wa(),{createReadableStream:s,createFileOutput:r}=Xa(),{transform:a,withAutomaticRetries:i,validateWebhook:o,parseProgressFromLogs:l,streamAsyncIterator:m}=pt(),y=Ga(),f=Va(),h=Ka(),u=Er(),g=Qa(),S=za(),d=Ya(),A=Za(),p=ei(),_=si;class w{constructor($={}){this.auth=$.auth||(typeof process<"u"?n.REPLICATE_API_TOKEN:null),this.userAgent=$.userAgent||`replicate-javascript/${_.version}`,this.baseUrl=$.baseUrl||"https://api.replicate.com/v1",this.fetch=$.fetch||globalThis.fetch,this.fileEncodingStrategy=$.fileEncodingStrategy||"default",this.useFileOutput=$.useFileOutput!==!1,this.accounts={current:y.current.bind(this)},this.collections={list:f.list.bind(this),get:f.get.bind(this)},this.deployments={get:h.get.bind(this),create:h.create.bind(this),update:h.update.bind(this),delete:h.delete.bind(this),list:h.list.bind(this),predictions:{create:h.predictions.create.bind(this)}},this.files={create:u.create.bind(this),get:u.get.bind(this),list:u.list.bind(this),delete:u.delete.bind(this)},this.hardware={list:g.list.bind(this)},this.models={get:S.get.bind(this),list:S.list.bind(this),create:S.create.bind(this),versions:{list:S.versions.list.bind(this),get:S.versions.get.bind(this)},search:S.search.bind(this)},this.predictions={create:d.create.bind(this),get:d.get.bind(this),cancel:d.cancel.bind(this),list:d.list.bind(this)},this.trainings={create:A.create.bind(this),get:A.get.bind(this),cancel:A.cancel.bind(this),list:A.list.bind(this)},this.webhooks={default:{secret:{get:p.default.secret.get.bind(this)}}}}async run($,v,I){const{wait:x={mode:"block"},signal:E,...R}=v,M=t.parse($);let F;if(M.version)F=await this.predictions.create({...R,version:M.version,wait:x.mode==="block"?x.timeout??!0:!1});else if(M.owner&&M.name)F=await this.predictions.create({...R,model:`${M.owner}/${M.name}`,wait:x.mode==="block"?x.timeout??!0:!1});else throw new Error("Invalid model version identifier");if(I&&I(F),x.mode==="block"&&F.status!=="starting"||(F=await this.wait(F,{interval:x.mode==="poll"?x.interval:void 0},async D=>(I&&I(D),!!(E&&E.aborted)))),E&&E.aborted&&(F=await this.predictions.cancel(F.id)),I&&I(F),F.status==="failed")throw new Error(`Prediction failed: ${F.error}`);return a(F.output,D=>typeof D=="string"&&(D.startsWith("https:")||D.startsWith("data:"))&&this.useFileOutput?r({url:D,fetch:this.fetch}):D)}async request($,v){const{auth:I,baseUrl:x,userAgent:E}=this;let R;$ instanceof URL?R=$:R=new URL($.startsWith("/")?$.slice(1):$,x.endsWith("/")?x:`${x}/`);const{method:M="GET",params:F={},data:L}=v;for(const[Z,Se]of Object.entries(F))R.searchParams.append(Z,Se);const D={"Content-Type":"application/json","User-Agent":E};if(I&&(D.Authorization=`Bearer ${I}`),v.headers)for(const[Z,Se]of Object.entries(v.headers))D[Z]=Se;let Y;L instanceof FormData?(Y=L,delete D["Content-Type"]):L&&(Y=JSON.stringify(L));const be={method:M,headers:D,body:Y},Ir=M==="GET"?Z=>Z.status===429||Z.status>=500:Z=>Z.status===429,Or=this.fetch,fe=await i(async()=>Or(R,be),{shouldRetry:Ir});if(!fe.ok){const Z=new Request(R,be),Se=await fe.text();throw new e(`Request to ${R} failed with status ${fe.status} ${fe.statusText}: ${Se}.`,Z,fe)}return fe}async*stream($,v){const{wait:I,signal:x,...E}=v,R=t.parse($);let M;if(R.version)M=await this.predictions.create({...E,version:R.version});else if(R.owner&&R.name)M=await this.predictions.create({...E,model:`${R.owner}/${R.name}`});else throw new Error("Invalid model version identifier");if(M.urls&&M.urls.stream){const F=s({url:M.urls.stream,fetch:this.fetch,...x?{options:{signal:x}}:{}});yield*m(F)}else throw new Error("Prediction does not support streaming")}async*paginate($){const v=await $();if(yield v.results,v.next){const I=()=>this.request(v.next,{method:"GET"}).then(x=>x.json());yield*this.paginate(I)}}async wait($,v,I){const{id:x}=$;if(!x)throw new Error("Invalid prediction");if($.status==="succeeded"||$.status==="failed"||$.status==="canceled")return $;const E=F=>new Promise(L=>setTimeout(L,F)),R=v&&v.interval||500;let M=await this.predictions.get(x);for(;M.status!=="succeeded"&&M.status!=="failed"&&M.status!=="canceled"&&!(I&&await I(M)===!0);)await E(R),M=await this.predictions.get($.id);if(M.status==="failed")throw new Error(`Prediction failed: ${M.error}`);return M}}return Ae.exports=w,Ae.exports.validateWebhook=o,Ae.exports.parseProgressFromLogs=l,Ae.exports}var ri=ni();const ai=kr(ri),Cr=new N({apiKey:"********************************************************************************************************************************************************************",dangerouslyAllowBrowser:!0});new ai({auth:"****************************************"});const mi=async(n,e,t)=>{var s,r;try{const a=`Generate a ${e} horoscope for ${n}. Make it mystical, insightful, and personalized. Include guidance on love, career, health, and spiritual growth. Keep it engaging and positive while being realistic.`;return((r=(s=(await Cr.chat.completions.create({model:"gpt-3.5-turbo",messages:[{role:"system",content:"You are a wise astrologer and mythology expert. Create engaging, mystical horoscopes that blend ancient wisdom with modern insights."},{role:"user",content:a}],max_tokens:300,temperature:.8})).choices[0])==null?void 0:s.message)==null?void 0:r.content)||""}catch(a){throw console.error("Error generating horoscope:",a),a}},pi=async n=>{var e,t;try{const s=`Based on the following user information, provide a mystical future reading that includes insights about their path ahead, opportunities, challenges, and spiritual guidance: ${JSON.stringify(n)}`;return((t=(e=(await Cr.chat.completions.create({model:"gpt-3.5-turbo",messages:[{role:"system",content:"You are an ancient oracle with deep mystical knowledge. Provide insightful, encouraging future readings that guide users toward their highest potential."},{role:"user",content:s}],max_tokens:400,temperature:.9})).choices[0])==null?void 0:e.message)==null?void 0:t.content)||""}catch(s){throw console.error("Error generating future reading:",s),s}};export{pi as a,mi as g};
